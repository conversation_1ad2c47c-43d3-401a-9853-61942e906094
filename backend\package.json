{"name": "rent-guardian-backend", "version": "1.0.0", "description": "Backend server for Rent Guardian", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^2.0.0"}, "devDependencies": {"nodemon": "^3.0.2"}}