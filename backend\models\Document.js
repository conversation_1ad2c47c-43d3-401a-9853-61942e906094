const mongoose = require('mongoose');

const documentSchema = new mongoose.Schema({
    tenant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Tenant',
        required: [true, 'Please provide tenant ID']
    },
    documentType: {
        type: String,
        required: [true, 'Please provide document type']
    },
    fileName: {
        type: String,
        required: [true, 'Please provide file name']
    },
    filePath: {
        type: String,
        required: [true, 'Please provide file path']
    },
    fileSize: {
        type: Number,
        required: [true, 'Please provide file size']
    },
    mimeType: {
        type: String,
        required: [true, 'Please provide mime type']
    },
    expiryDate: {
        type: Date
    },
    uploadDate: {
        type: Date,
        default: Date.now
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    }
});

module.exports = mongoose.model('Document', documentSchema); 