import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, Edit, Trash2, Download, Save, X } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card } from './ui/card';
import { Label } from './ui/label';
import { getAllRentEntries, updateRentEntry, deleteRentEntry, type RentEntry } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from './ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';

interface AllRentEntriesProps {
  onViewEntry?: (entryId: string) => void;
  onEditEntry?: (entryId: string) => void;
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'paid':
      return 'Paid';
    case 'pending':
      return 'Pending';
    case 'partial':
      return 'Partial';
    default:
      return status ? status.charAt(0).toUpperCase() + status.slice(1) : '';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'pending':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'partial':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    default:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
  }
};

export const AllRentEntries: React.FC<AllRentEntriesProps> = ({
  onViewEntry,
  onEditEntry
}) => {
  const { toast } = useToast();
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [monthFilter, setMonthFilter] = useState('');
  const [editingEntry, setEditingEntry] = useState<RentEntry | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editFormData, setEditFormData] = useState<Partial<RentEntry>>({});
  const [viewingEntry, setViewingEntry] = useState<RentEntry | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  useEffect(() => {
    loadRentEntries();
  }, []);

  const loadRentEntries = async () => {
    try {
      setIsLoading(true);
      const response = await getAllRentEntries();
      setRentEntries(response.data || []);
    } catch (error: any) {
      console.error('Error loading rent entries:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load rent entries. Please check if the server is running.",
        variant: "destructive",
      });
      setRentEntries([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteEntry = async (entry: RentEntry) => {
    if (!entry.id) {
      toast({
        title: "Error",
        description: "Cannot delete entry: No entry ID found",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsDeleting(true);
      await deleteRentEntry(entry.id);

      // Remove entry from local state
      setRentEntries(prevEntries => prevEntries.filter(e => e.id !== entry.id));

      toast({
        title: "Success",
        description: "Rent entry has been deleted successfully",
      });
    } catch (error: any) {
      console.error('Error deleting rent entry:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete rent entry",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditEntry = (entry: RentEntry) => {
    setEditingEntry(entry);
    setEditFormData({
      rentDetails: { ...entry.rentDetails },
      paymentStatus: entry.paymentStatus
    });
    setIsEditModalOpen(true);
  };

  const handleViewEntry = (entry: RentEntry) => {
    setViewingEntry(entry);
    setIsViewModalOpen(true);
  };

  const handleUpdateEntry = async () => {
    if (!editingEntry?.id || !editFormData.rentDetails) {
      toast({
        title: "Error",
        description: "Invalid entry data",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUpdating(true);
      const response = await updateRentEntry(editingEntry.id, editFormData);

      // Update entry in local state
      setRentEntries(prevEntries =>
        prevEntries.map(entry =>
          entry.id === editingEntry.id
            ? { ...entry, ...editFormData, rentDetails: editFormData.rentDetails! }
            : entry
        )
      );

      toast({
        title: "Success",
        description: "Rent entry updated successfully",
      });

      setIsEditModalOpen(false);
      setEditingEntry(null);
      setEditFormData({});
    } catch (error: any) {
      console.error('Error updating rent entry:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update rent entry",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleFormChange = (field: string, value: any) => {
    if (field.startsWith('rentDetails.')) {
      const rentField = field.replace('rentDetails.', '');
      setEditFormData(prev => ({
        ...prev,
        rentDetails: {
          ...prev.rentDetails!,
          [rentField]: value
        }
      }));
    } else {
      setEditFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Calculate totals when rent details change
  useEffect(() => {
    if (editFormData.rentDetails) {
      const { cashRent = 0, bankRent = 0, roomRent = 0, gst = 18 } = editFormData.rentDetails;
      const gstAmount = Math.round(bankRent * (gst / 100));
      const totalRent = cashRent + bankRent + roomRent;
      const totalPayable = totalRent + gstAmount;

      setEditFormData(prev => ({
        ...prev,
        rentDetails: {
          ...prev.rentDetails!,
          gstAmount,
          totalRent,
          totalPayable
        }
      }));
    }
  }, [editFormData.rentDetails?.cashRent, editFormData.rentDetails?.bankRent, editFormData.rentDetails?.roomRent]);

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setMonthFilter('');
  };

  // Filter rent entries
  const filteredEntries = rentEntries.filter(entry => {
    const tenantName = typeof entry.tenant === 'object' ? entry.tenant.fullName : '';
    const matchesSearch = tenantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.building.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || entry.paymentStatus === statusFilter;

    const matchesMonth = !monthFilter ||
      format(new Date(entry.rentMonth), 'yyyy-MM') === monthFilter;

    return matchesSearch && matchesStatus && matchesMonth;
  });

  // Sort entries by rent month (newest first)
  const sortedEntries = filteredEntries.sort((a, b) =>
    new Date(b.rentMonth).getTime() - new Date(a.rentMonth).getTime()
  );

  // Calculate summary statistics
  const totalEntries = sortedEntries.length;
  const totalRentCollected = sortedEntries
    .filter(entry => entry.paymentStatus === 'paid')
    .reduce((sum, entry) => sum + entry.rentDetails.totalRent, 0);
  const totalGSTCollected = sortedEntries
    .filter(entry => entry.paymentStatus === 'paid')
    .reduce((sum, entry) => sum + entry.rentDetails.gstAmount, 0);
  const pendingAmount = sortedEntries
    .filter(entry => entry.paymentStatus === 'pending')
    .reduce((sum, entry) => sum + entry.rentDetails.totalPayable, 0);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-500 dark:text-gray-400">Loading rent entries...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">All Rent Entries</h1>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export Data
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search by tenant or building..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All Status</option>
              <option value="paid">Paid</option>
              <option value="pending">Pending</option>
              <option value="partial">Partial</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Month
            </label>
            <Input
              type="month"
              value={monthFilter}
              onChange={(e) => setMonthFilter(e.target.value)}
            />
          </div>

          <div className="flex items-end">
            <Button variant="outline" onClick={clearFilters} className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Clear Filters
            </Button>
          </div>
        </div>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="text-center">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Entries</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalEntries}</p>
          </div>
        </Card>

        <Card className="p-6">
          <div className="text-center">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Rent Collected</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">₹{totalRentCollected.toLocaleString()}</p>
          </div>
        </Card>

        <Card className="p-6">
          <div className="text-center">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total GST Collected</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">₹{totalGSTCollected.toLocaleString()}</p>
          </div>
        </Card>

        <Card className="p-6">
          <div className="text-center">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Amount</p>
            <p className="text-2xl font-bold text-red-600 dark:text-red-400">₹{pendingAmount.toLocaleString()}</p>
          </div>
        </Card>
      </div>

      {/* Rent Entries Table */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Month
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Tenant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Property
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Cash Rent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Bank Rent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  GST (18%)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Room Rent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total Rent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total Payable
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Paid Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {sortedEntries.length === 0 ? (
                <tr>
                  <td colSpan={12} className="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center">
                      <Search className="h-12 w-12 mb-4 opacity-50" />
                      <p className="text-lg font-medium">No rent entries found</p>
                      <p className="text-sm">Try adjusting your search criteria or filters.</p>
                    </div>
                  </td>
                </tr>
              ) : (
                sortedEntries.map((entry) => {
                  const tenantName = typeof entry.tenant === 'object' ? entry.tenant.fullName : 'Unknown';
                  const paidDate = entry.paymentStatus === 'paid' && entry.updatedAt
                    ? format(new Date(entry.updatedAt), 'dd/MM/yyyy')
                    : '-';

                  return (
                    <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {format(new Date(entry.rentMonth), 'yyyy-MM')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {tenantName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {entry.building} - {entry.floor}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        ₹{entry.rentDetails.cashRent.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        ₹{entry.rentDetails.bankRent.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        ₹{entry.rentDetails.gstAmount.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        ₹{entry.rentDetails.roomRent.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        ₹{entry.rentDetails.totalRent.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 dark:text-blue-400">
                        ₹{entry.rentDetails.totalPayable.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(entry.paymentStatus)}`}>
                          {getStatusText(entry.paymentStatus)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {paidDate}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {entry.id && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewEntry(entry)}
                                title="View Details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditEntry(entry)}
                                title="Edit Entry"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    title="Delete Entry"
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Delete Rent Entry</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to delete this rent entry for <strong>{tenantName}</strong>
                                      ({format(new Date(entry.rentMonth), 'MMMM yyyy')})?
                                      This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeleteEntry(entry)}
                                      disabled={isDeleting}
                                      className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
                                    >
                                      {isDeleting ? 'Deleting...' : 'Delete'}
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Rent Entry</DialogTitle>
            <DialogDescription>
              {editingEntry && (
                <>
                  Editing rent entry for{' '}
                  <strong>
                    {typeof editingEntry.tenant === 'object' ? editingEntry.tenant.fullName : 'Unknown'}
                  </strong>{' '}
                  - {format(new Date(editingEntry.rentMonth), 'MMMM yyyy')}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          {editFormData.rentDetails && (
            <div className="grid grid-cols-2 gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="cashRent">Cash Rent</Label>
                <Input
                  id="cashRent"
                  type="number"
                  value={editFormData.rentDetails.cashRent || 0}
                  onChange={(e) => handleFormChange('rentDetails.cashRent', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bankRent">Bank Rent</Label>
                <Input
                  id="bankRent"
                  type="number"
                  value={editFormData.rentDetails.bankRent || 0}
                  onChange={(e) => handleFormChange('rentDetails.bankRent', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="roomRent">Room Rent</Label>
                <Input
                  id="roomRent"
                  type="number"
                  value={editFormData.rentDetails.roomRent || 0}
                  onChange={(e) => handleFormChange('rentDetails.roomRent', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gst">GST (%)</Label>
                <Input
                  id="gst"
                  type="number"
                  value={editFormData.rentDetails.gst || 18}
                  onChange={(e) => handleFormChange('rentDetails.gst', Number(e.target.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gstAmount">GST Amount (Auto-calculated)</Label>
                <Input
                  id="gstAmount"
                  type="number"
                  value={editFormData.rentDetails.gstAmount || 0}
                  disabled
                  className="bg-gray-100 dark:bg-gray-800"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="totalRent">Total Rent (Auto-calculated)</Label>
                <Input
                  id="totalRent"
                  type="number"
                  value={editFormData.rentDetails.totalRent || 0}
                  disabled
                  className="bg-gray-100 dark:bg-gray-800"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="totalPayable">Total Payable (Auto-calculated)</Label>
                <Input
                  id="totalPayable"
                  type="number"
                  value={editFormData.rentDetails.totalPayable || 0}
                  disabled
                  className="bg-gray-100 dark:bg-gray-800"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="paymentStatus">Payment Status</Label>
                <select
                  id="paymentStatus"
                  value={editFormData.paymentStatus || 'pending'}
                  onChange={(e) => handleFormChange('paymentStatus', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="pending">Pending</option>
                  <option value="partial">Partial</option>
                  <option value="paid">Paid</option>
                </select>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditModalOpen(false)}
              disabled={isUpdating}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleUpdateEntry}
              disabled={isUpdating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isUpdating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Entry
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Rent Entry Details</DialogTitle>
            <DialogDescription>
              {viewingEntry && (
                <>
                  Rent entry for{' '}
                  <strong>
                    {typeof viewingEntry.tenant === 'object' ? viewingEntry.tenant.fullName : 'Unknown'}
                  </strong>{' '}
                  - {format(new Date(viewingEntry.rentMonth), 'MMMM yyyy')}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          {viewingEntry && (
            <div className="space-y-6">
              {/* Tenant Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Tenant Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Name:</span>
                    <span className="ml-2 text-gray-900 dark:text-white">
                      {typeof viewingEntry.tenant === 'object' ? viewingEntry.tenant.fullName : 'Unknown'}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Property:</span>
                    <span className="ml-2 text-gray-900 dark:text-white">
                      {viewingEntry.building} - {viewingEntry.floor}
                    </span>
                  </div>
                </div>
              </div>

              {/* Rent Details */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Rent Breakdown</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Cash Rent:</span>
                    <span className="text-gray-900 dark:text-white">₹{viewingEntry.rentDetails.cashRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Bank Rent:</span>
                    <span className="text-gray-900 dark:text-white">₹{viewingEntry.rentDetails.bankRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Room Rent:</span>
                    <span className="text-gray-900 dark:text-white">₹{viewingEntry.rentDetails.roomRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">GST ({viewingEntry.rentDetails.gst}%):</span>
                    <span className="text-gray-900 dark:text-white">₹{viewingEntry.rentDetails.gstAmount.toLocaleString()}</span>
                  </div>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 mt-4 pt-4">
                  <div className="grid grid-cols-2 gap-4 text-sm font-medium">
                    <div className="flex justify-between">
                      <span className="text-gray-900 dark:text-white">Total Rent:</span>
                      <span className="text-gray-900 dark:text-white">₹{viewingEntry.rentDetails.totalRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-600 dark:text-blue-400">Total Payable:</span>
                      <span className="text-blue-600 dark:text-blue-400">₹{viewingEntry.rentDetails.totalPayable.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Status */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Payment Information</h4>
                <div className="flex items-center space-x-4">
                  <span className="text-gray-500 dark:text-gray-400">Status:</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(viewingEntry.paymentStatus)}`}>
                    {getStatusText(viewingEntry.paymentStatus)}
                  </span>
                  {viewingEntry.paymentStatus === 'paid' && viewingEntry.updatedAt && (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Paid on: {format(new Date(viewingEntry.updatedAt), 'dd/MM/yyyy')}
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsViewModalOpen(false)}
            >
              Close
            </Button>
            <Button
              onClick={() => {
                setIsViewModalOpen(false);
                if (viewingEntry) {
                  handleEditEntry(viewingEntry);
                }
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Entry
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
