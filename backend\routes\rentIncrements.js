const express = require('express');
const router = express.Router();
const RentIncrement = require('../models/RentIncrement');
const Tenant = require('../models/Tenant');
const { protect } = require('../middleware/auth');

// Get all rent increments with filters
router.get('/', protect, async (req, res) => {
    try {
        const {
            tenant,
            status,
            startDate,
            endDate,
            building,
            page = 1,
            limit = 10,
            sortBy = 'effectiveDate',
            sortOrder = 'desc'
        } = req.query;

        // Build filter object
        const filter = {};
        
        if (tenant) filter.tenant = tenant;
        if (status) filter.status = status;
        if (building) {
            // Find tenants in the specified building
            const tenantsInBuilding = await Tenant.find({ building }).select('_id');
            filter.tenant = { $in: tenantsInBuilding.map(t => t._id) };
        }
        
        if (startDate || endDate) {
            filter.effectiveDate = {};
            if (startDate) filter.effectiveDate.$gte = new Date(startDate);
            if (endDate) filter.effectiveDate.$lte = new Date(endDate);
        }

        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get rent increments with populated tenant data
        const rentIncrements = await RentIncrement.find(filter)
            .populate('tenant', 'fullName firmName building floor phone email')
            .populate('createdBy', 'name email')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const total = await RentIncrement.countDocuments(filter);

        res.json({
            success: true,
            data: rentIncrements,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get upcoming rent increments
router.get('/upcoming', protect, async (req, res) => {
    try {
        const { daysAhead = 90 } = req.query;
        
        // Get all active tenants with next increment dates
        const tenants = await Tenant.find({
            isActive: true,
            nextIncrementDate: { $exists: true, $ne: null }
        });

        const today = new Date();
        const futureDate = new Date();
        futureDate.setDate(today.getDate() + parseInt(daysAhead));

        const upcomingIncrements = [];

        for (const tenant of tenants) {
            const incrementDate = new Date(tenant.nextIncrementDate);
            
            // Only include if increment date is within the specified range
            if (incrementDate >= today && incrementDate <= futureDate) {
                const daysRemaining = Math.ceil((incrementDate - today) / (1000 * 60 * 60 * 24));
                
                // Determine status
                let status = 'upcoming';
                if (daysRemaining < 0) {
                    status = 'overdue';
                } else if (daysRemaining <= 30) {
                    status = 'due';
                }

                const currentRent = tenant.rentDetails.totalRent;
                const incrementPercentage = 5; // Default 5% increment
                const newRent = Math.round(currentRent * (1 + incrementPercentage / 100));

                upcomingIncrements.push({
                    tenant,
                    currentRent,
                    newRent,
                    incrementPercentage,
                    incrementDate: tenant.nextIncrementDate,
                    daysRemaining,
                    status
                });
            }
        }

        // Sort by days remaining (ascending)
        upcomingIncrements.sort((a, b) => a.daysRemaining - b.daysRemaining);

        res.json({
            success: true,
            data: upcomingIncrements
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Apply rent increment
router.post('/apply', protect, async (req, res) => {
    try {
        const {
            tenantId,
            incrementPercentage,
            effectiveDate,
            reason,
            notes
        } = req.body;

        // Get tenant details
        const tenant = await Tenant.findById(tenantId);
        if (!tenant) {
            return res.status(404).json({ message: 'Tenant not found' });
        }

        const oldRent = tenant.rentDetails.totalRent;
        const newRent = Math.round(oldRent * (1 + incrementPercentage / 100));

        // Calculate new rent details proportionally
        const rentRatio = newRent / oldRent;
        const newRentDetails = {
            cashRent: Math.round(tenant.rentDetails.cashRent * rentRatio),
            bankRent: Math.round(tenant.rentDetails.bankRent * rentRatio),
            roomRent: Math.round(tenant.rentDetails.roomRent * rentRatio),
            gst: tenant.rentDetails.gst,
            totalRent: newRent
        };
        
        // Calculate GST on new bank rent
        newRentDetails.gstAmount = Math.round(newRentDetails.bankRent * (newRentDetails.gst / 100));
        newRentDetails.totalPayable = newRentDetails.totalRent + newRentDetails.gstAmount;

        // Create rent increment record
        const rentIncrement = await RentIncrement.create({
            tenant: tenantId,
            oldRent,
            newRent,
            incrementPercentage,
            effectiveDate: effectiveDate || new Date(),
            reason: reason || 'Annual increment',
            notes,
            oldRentDetails: tenant.rentDetails,
            newRentDetails,
            createdBy: req.user.id
        });

        // Update tenant with new rent details and next increment date
        const nextIncrementDate = new Date(effectiveDate || new Date());
        nextIncrementDate.setFullYear(nextIncrementDate.getFullYear() + 1);

        await Tenant.findByIdAndUpdate(tenantId, {
            rentDetails: newRentDetails,
            nextIncrementDate: nextIncrementDate.toISOString().split('T')[0],
            updatedAt: new Date()
        });

        // Populate the created increment
        const populatedIncrement = await RentIncrement.findById(rentIncrement._id)
            .populate('tenant', 'fullName firmName building floor')
            .populate('createdBy', 'name email');

        res.status(201).json({
            success: true,
            data: populatedIncrement
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get rent increment history for a tenant
router.get('/tenant/:tenantId', protect, async (req, res) => {
    try {
        const { tenantId } = req.params;

        const increments = await RentIncrement.find({ tenant: tenantId })
            .populate('createdBy', 'name email')
            .sort({ effectiveDate: -1 });

        res.json({
            success: true,
            data: increments
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get rent increment statistics
router.get('/stats', protect, async (req, res) => {
    try {
        const today = new Date();
        const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const currentQuarter = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

        // Get upcoming increments
        const upcomingIncrements = await Tenant.find({
            isActive: true,
            nextIncrementDate: { $exists: true, $ne: null }
        });

        const stats = {
            dueThisMonth: 0,
            expectedIncrease: 0,
            appliedThisQuarter: 0,
            totalUpcoming: upcomingIncrements.length,
            overdueCount: 0
        };

        // Calculate stats from upcoming increments
        upcomingIncrements.forEach(tenant => {
            const incrementDate = new Date(tenant.nextIncrementDate);
            const daysRemaining = Math.ceil((incrementDate - today) / (1000 * 60 * 60 * 24));
            
            // Due this month
            if (incrementDate >= currentMonth && incrementDate < nextMonth) {
                stats.dueThisMonth++;
            }

            // Overdue
            if (daysRemaining < 0) {
                stats.overdueCount++;
            }

            // Expected increase (for due and overdue)
            if (daysRemaining <= 30) {
                const currentRent = tenant.rentDetails.totalRent;
                const newRent = Math.round(currentRent * 1.05); // 5% default
                stats.expectedIncrease += (newRent - currentRent);
            }
        });

        // Applied this quarter
        const appliedThisQuarter = await RentIncrement.countDocuments({
            appliedDate: { $gte: currentQuarter },
            status: 'applied'
        });
        stats.appliedThisQuarter = appliedThisQuarter;

        res.json({
            success: true,
            data: stats
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

module.exports = router;
