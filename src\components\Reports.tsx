import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { ArrowDownToLine, FileDown, Filter, TrendingUp, TrendingDown, DollarSign, Users, Building2, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getAllRentEntries, getAllTenants, type RentEntry, type Tenant } from '@/services/api';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface CollectionSummary {
  building: string;
  rent: number;
  gst: number;
  total: number;
}

interface PendingCollection {
  tenant: Tenant;
  amount: number;
  gstAmount: number;
  building: string;
  floor: string;
}

export const Reports = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [reportPeriod, setReportPeriod] = useState('This Month');
  const [selectedBuilding, setSelectedBuilding] = useState('All Buildings');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  // Summary statistics
  const [stats, setStats] = useState({
    rentCollected: 0,
    gstCollected: 0,
    pendingRent: 0,
    pendingGST: 0,
    totalDeposits: 0,
    totalTenants: 0
  });

  const [collectionSummary, setCollectionSummary] = useState<CollectionSummary[]>([]);
  const [pendingCollections, setPendingCollections] = useState<PendingCollection[]>([]);

  const buildings = ['All Buildings', 'Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const periods = ['This Month', 'This Quarter', 'This Year', 'Custom Range'];

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateSummary();
  }, [rentEntries, tenants, reportPeriod, selectedBuilding]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [entriesResponse, tenantsResponse] = await Promise.all([
        getAllRentEntries(),
        getAllTenants()
      ]);

      setRentEntries(entriesResponse.data || []);
      setTenants(tenantsResponse.data || []);
    } catch (error: any) {
      console.error('Error loading report data:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load report data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateSummary = () => {
    // Filter entries based on period and building
    const filteredEntries = rentEntries.filter(entry => {
      const entryDate = new Date(entry.rentMonth);
      const now = new Date();

      // Period filter
      let isInPeriod = false;
      switch (reportPeriod) {
        case 'This Month':
          isInPeriod = entryDate.getMonth() === now.getMonth() &&
            entryDate.getFullYear() === now.getFullYear();
          break;
        case 'This Quarter':
          const quarter = Math.floor(now.getMonth() / 3);
          const entryQuarter = Math.floor(entryDate.getMonth() / 3);
          isInPeriod = entryQuarter === quarter &&
            entryDate.getFullYear() === now.getFullYear();
          break;
        case 'This Year':
          isInPeriod = entryDate.getFullYear() === now.getFullYear();
          break;
        case 'Custom Range':
          if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            isInPeriod = entryDate >= start && entryDate <= end;
          } else {
            isInPeriod = true;
          }
          break;
        default:
          isInPeriod = true;
      }

      // Building filter
      const isBuildingMatch = selectedBuilding === 'All Buildings' ||
        entry.building === selectedBuilding;

      return isInPeriod && isBuildingMatch;
    });

    // Calculate collection summary by building
    const summary = filteredEntries.reduce((acc, entry) => {
      const building = entry.building;
      if (!acc[building]) {
        acc[building] = { building, rent: 0, gst: 0, total: 0 };
      }
      acc[building].rent += entry.rentDetails.totalRent;
      acc[building].gst += entry.rentDetails.gstAmount;
      acc[building].total += entry.rentDetails.totalPayable;
      return acc;
    }, {} as Record<string, CollectionSummary>);

    // Calculate pending collections based on the selected period
    const pending = tenants
      .filter(tenant => selectedBuilding === 'All Buildings' || tenant.building === selectedBuilding)
      .map(tenant => {
        // Get all entries for this tenant in the selected period
        const tenantEntries = filteredEntries
          .filter(e => e.tenant === tenant.id && e.paymentStatus === 'pending')
          .sort((a, b) => new Date(b.rentMonth).getTime() - new Date(a.rentMonth).getTime());

        if (tenantEntries.length === 0) return null;

        // Calculate total pending amount for this tenant
        const totalPending = tenantEntries.reduce((sum, entry) => sum + entry.rentDetails.totalRent, 0);
        const totalPendingGST = tenantEntries.reduce((sum, entry) => sum + entry.rentDetails.gstAmount, 0);

        return {
          tenant,
          amount: totalPending,
          gstAmount: totalPendingGST,
          building: tenant.building,
          floor: tenant.floor
        };
      })
      .filter((p): p is PendingCollection => p !== null && (p.amount > 0 || p.gstAmount > 0));

    // Calculate overall statistics
    const stats = {
      rentCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.totalRent : 0), 0),
      gstCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.gstAmount : 0), 0),
      pendingRent: pending.reduce((sum, p) => sum + p.amount, 0),
      pendingGST: pending.reduce((sum, p) => sum + p.gstAmount, 0),
      totalDeposits: tenants.reduce((sum, tenant) => sum + tenant.deposit, 0),
      totalTenants: tenants.length
    };

    setCollectionSummary(Object.values(summary));
    setPendingCollections(pending);
    setStats(stats);
  };

  const handleExportPDF = () => {
    // Implement PDF export functionality
    toast({
      title: "Coming Soon",
      description: "PDF export functionality will be available soon",
    });
  };

  const handleExportExcel = () => {
    // Implement Excel export functionality
    toast({
      title: "Coming Soon",
      description: "Excel export functionality will be available soon",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="space-y-2">
              <Label htmlFor="report-period">Report Period</Label>
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger id="report-period">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map(period => (
                    <SelectItem key={period} value={period}>{period}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="building">Building</Label>
              <Select value={selectedBuilding} onValueChange={setSelectedBuilding}>
                <SelectTrigger id="building">
                  <SelectValue placeholder="Select building" />
                </SelectTrigger>
                <SelectContent>
                  {buildings.map(building => (
                    <SelectItem key={building} value={building}>{building}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {reportPeriod === 'Custom Range' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="start-date">Start Date</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleExportPDF}>
              <FileDown className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" onClick={handleExportExcel}>
              <ArrowDownToLine className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rent Collected</p>
                <p className="text-2xl font-bold text-green-600">₹{stats.rentCollected.toLocaleString()}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">GST Collected</p>
                <p className="text-2xl font-bold text-blue-600">₹{stats.gstCollected.toLocaleString()}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <DollarSign className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Rent</p>
                <p className="text-2xl font-bold text-red-600">₹{stats.pendingRent.toLocaleString()}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                <TrendingDown className="h-4 w-4 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending GST</p>
                <p className="text-2xl font-bold text-orange-600">₹{stats.pendingGST.toLocaleString()}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
                <Calendar className="h-4 w-4 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Deposits</p>
                <p className="text-2xl font-bold text-purple-600">₹{stats.totalDeposits.toLocaleString()}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                <Building2 className="h-4 w-4 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Tenants</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalTenants}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                <Users className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Collection Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Monthly Collection Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Building</th>
                  <th className="text-right py-3 px-4 font-medium text-muted-foreground">Rent</th>
                  <th className="text-right py-3 px-4 font-medium text-muted-foreground">GST</th>
                  <th className="text-right py-3 px-4 font-medium text-muted-foreground">Total</th>
                </tr>
              </thead>
              <tbody>
                {collectionSummary.map((summary, index) => (
                  <tr key={index} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-4 font-medium">{summary.building}</td>
                    <td className="py-3 px-4 text-right text-green-600 font-medium">₹{summary.rent.toLocaleString()}</td>
                    <td className="py-3 px-4 text-right text-blue-600 font-medium">₹{summary.gst.toLocaleString()}</td>
                    <td className="py-3 px-4 text-right font-bold">₹{summary.total.toLocaleString()}</td>
                  </tr>
                ))}
                <tr className="border-b-2 border-primary bg-muted/30">
                  <td className="py-4 px-4 font-bold text-lg">Grand Total</td>
                  <td className="py-4 px-4 text-right font-bold text-lg text-green-600">₹{stats.rentCollected.toLocaleString()}</td>
                  <td className="py-4 px-4 text-right font-bold text-lg text-blue-600">₹{stats.gstCollected.toLocaleString()}</td>
                  <td className="py-4 px-4 text-right font-bold text-lg text-primary">₹{(stats.rentCollected + stats.gstCollected).toLocaleString()}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pending Collections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5 text-red-500" />
            Pending Collections
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pendingCollections.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No pending collections found</p>
              </div>
            ) : (
              pendingCollections.map((pending, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                      <Users className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <div className="font-medium">{pending.tenant.fullName}</div>
                      <div className="text-sm text-muted-foreground">{pending.building} - {pending.floor}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-red-600 font-bold">₹{pending.amount.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">+ ₹{pending.gstAmount.toLocaleString()} GST</div>
                  </div>
                </div>
              ))
            )}

            {pendingCollections.length > 0 && (
              <div className="mt-6 pt-6 border-t space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-muted-foreground">Total Pending Rent:</span>
                  <span className="font-bold text-red-600">₹{stats.pendingRent.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium text-muted-foreground">Total Pending GST:</span>
                  <span className="font-bold text-orange-600">₹{stats.pendingGST.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center pt-3 border-t">
                  <span className="font-bold text-lg">Total Pending:</span>
                  <span className="font-bold text-lg text-red-700">₹{(stats.pendingRent + stats.pendingGST).toLocaleString()}</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
