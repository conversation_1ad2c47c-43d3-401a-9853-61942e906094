import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { ArrowDownToLine, FileDown, Filter } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getAllRentEntries, getAllTenants, type RentEntry, type Tenant } from '@/services/api';
import { Input } from './ui/input';

interface CollectionSummary {
  building: string;
  rent: number;
  gst: number;
  total: number;
}

interface PendingCollection {
  tenant: Tenant;
  amount: number;
  gstAmount: number;
  building: string;
  floor: string;
}

export const Reports = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [reportPeriod, setReportPeriod] = useState('This Month');
  const [selectedBuilding, setSelectedBuilding] = useState('All Buildings');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  // Summary statistics
  const [stats, setStats] = useState({
    rentCollected: 0,
    gstCollected: 0,
    pendingRent: 0,
    pendingGST: 0,
    totalDeposits: 0,
    totalTenants: 0
  });

  const [collectionSummary, setCollectionSummary] = useState<CollectionSummary[]>([]);
  const [pendingCollections, setPendingCollections] = useState<PendingCollection[]>([]);

  const buildings = ['All Buildings', 'Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const periods = ['This Month', 'This Quarter', 'This Year', 'Custom Range'];

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateSummary();
  }, [rentEntries, tenants, reportPeriod, selectedBuilding]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [entriesResponse, tenantsResponse] = await Promise.all([
        getAllRentEntries(),
        getAllTenants()
      ]);

      setRentEntries(entriesResponse.data || []);
      setTenants(tenantsResponse.data || []);
    } catch (error: any) {
      console.error('Error loading report data:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load report data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateSummary = () => {
    // Filter entries based on period and building
    const filteredEntries = rentEntries.filter(entry => {
      const entryDate = new Date(entry.rentMonth);
      const now = new Date();

      // Period filter
      let isInPeriod = false;
      switch (reportPeriod) {
        case 'This Month':
          isInPeriod = entryDate.getMonth() === now.getMonth() &&
            entryDate.getFullYear() === now.getFullYear();
          break;
        case 'This Quarter':
          const quarter = Math.floor(now.getMonth() / 3);
          const entryQuarter = Math.floor(entryDate.getMonth() / 3);
          isInPeriod = entryQuarter === quarter &&
            entryDate.getFullYear() === now.getFullYear();
          break;
        case 'This Year':
          isInPeriod = entryDate.getFullYear() === now.getFullYear();
          break;
        case 'Custom Range':
          if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            isInPeriod = entryDate >= start && entryDate <= end;
          } else {
            isInPeriod = true;
          }
          break;
        default:
          isInPeriod = true;
      }

      // Building filter
      const isBuildingMatch = selectedBuilding === 'All Buildings' ||
        entry.building === selectedBuilding;

      return isInPeriod && isBuildingMatch;
    });

    // Calculate collection summary by building
    const summary = filteredEntries.reduce((acc, entry) => {
      const building = entry.building;
      if (!acc[building]) {
        acc[building] = { building, rent: 0, gst: 0, total: 0 };
      }
      acc[building].rent += entry.rentDetails.totalRent;
      acc[building].gst += entry.rentDetails.gstAmount;
      acc[building].total += entry.rentDetails.totalPayable;
      return acc;
    }, {} as Record<string, CollectionSummary>);

    // Calculate pending collections based on the selected period
    const pending = tenants
      .filter(tenant => selectedBuilding === 'All Buildings' || tenant.building === selectedBuilding)
      .map(tenant => {
        // Get all entries for this tenant in the selected period
        const tenantEntries = filteredEntries
          .filter(e => e.tenant === tenant.id && e.paymentStatus === 'pending')
          .sort((a, b) => new Date(b.rentMonth).getTime() - new Date(a.rentMonth).getTime());

        if (tenantEntries.length === 0) return null;

        // Calculate total pending amount for this tenant
        const totalPending = tenantEntries.reduce((sum, entry) => sum + entry.rentDetails.totalRent, 0);
        const totalPendingGST = tenantEntries.reduce((sum, entry) => sum + entry.rentDetails.gstAmount, 0);

        return {
          tenant,
          amount: totalPending,
          gstAmount: totalPendingGST,
          building: tenant.building,
          floor: tenant.floor
        };
      })
      .filter((p): p is PendingCollection => p !== null && (p.amount > 0 || p.gstAmount > 0));

    // Calculate overall statistics
    const stats = {
      rentCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.totalRent : 0), 0),
      gstCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.gstAmount : 0), 0),
      pendingRent: pending.reduce((sum, p) => sum + p.amount, 0),
      pendingGST: pending.reduce((sum, p) => sum + p.gstAmount, 0),
      totalDeposits: tenants.reduce((sum, tenant) => sum + tenant.deposit, 0),
      totalTenants: tenants.length
    };

    setCollectionSummary(Object.values(summary));
    setPendingCollections(pending);
    setStats(stats);
  };

  const handleExportPDF = () => {
    // Implement PDF export functionality
    toast({
      title: "Coming Soon",
      description: "PDF export functionality will be available soon",
    });
  };

  const handleExportExcel = () => {
    // Implement Excel export functionality
    toast({
      title: "Coming Soon",
      description: "Excel export functionality will be available soon",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Filters */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Report Filters</h3>
        <div className="flex flex-wrap items-end gap-4">
          <div className="flex-1 min-w-[200px]">
            <Label className="mb-2">Report Period</Label>
            <select
              value={reportPeriod}
              onChange={(e) => setReportPeriod(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {periods.map(period => (
                <option key={period} value={period}>{period}</option>
              ))}
            </select>
          </div>
          <div className="flex-1 min-w-[200px]">
            <Label className="mb-2">Building</Label>
            <select
              value={selectedBuilding}
              onChange={(e) => setSelectedBuilding(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {buildings.map(building => (
                <option key={building} value={building}>{building}</option>
              ))}
            </select>
          </div>
          {reportPeriod === 'Custom Range' && (
            <>
              <div className="flex-1 min-w-[200px]">
                <Label className="mb-2">Start Date</Label>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  placeholder="dd-mm-yyyy"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <Label className="mb-2">End Date</Label>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  placeholder="dd-mm-yyyy"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </>
          )}
          <div className="flex gap-2">
            <Button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 h-[38px]">
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleExportPDF} className="px-4 py-2 h-[38px]">
              <FileDown className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" onClick={handleExportExcel} className="px-4 py-2 h-[38px]">
              <ArrowDownToLine className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </div>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="p-4">
          <h4 className="text-sm font-medium text-gray-500">Rent Collected</h4>
          <p className="text-2xl font-bold text-green-600">₹{stats.rentCollected.toLocaleString()}</p>
        </Card>
        <Card className="p-4">
          <h4 className="text-sm font-medium text-gray-500">GST Collected</h4>
          <p className="text-2xl font-bold text-blue-600">₹{stats.gstCollected.toLocaleString()}</p>
        </Card>
        <Card className="p-4">
          <h4 className="text-sm font-medium text-gray-500">Pending Rent</h4>
          <p className="text-2xl font-bold text-red-600">₹{stats.pendingRent.toLocaleString()}</p>
        </Card>
        <Card className="p-4">
          <h4 className="text-sm font-medium text-gray-500">Pending GST</h4>
          <p className="text-2xl font-bold text-orange-600">₹{stats.pendingGST.toLocaleString()}</p>
        </Card>
        <Card className="p-4">
          <h4 className="text-sm font-medium text-gray-500">Total Deposits</h4>
          <p className="text-2xl font-bold text-purple-600">₹{stats.totalDeposits.toLocaleString()}</p>
        </Card>
        <Card className="p-4">
          <h4 className="text-sm font-medium text-gray-500">Total Tenants</h4>
          <p className="text-2xl font-bold text-gray-900">{stats.totalTenants}</p>
        </Card>
      </div>

      {/* Collection Summary */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Monthly Collection Summary</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Building</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GST</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {collectionSummary.map((summary, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{summary.building}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{summary.rent.toLocaleString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{summary.gst.toLocaleString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">₹{summary.total.toLocaleString()}</td>
                </tr>
              ))}
              <tr className="bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">Total</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">₹{stats.rentCollected.toLocaleString()}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">₹{stats.gstCollected.toLocaleString()}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">₹{(stats.rentCollected + stats.gstCollected).toLocaleString()}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </Card>

      {/* Pending Collections */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Pending Collections</h3>
        <div className="space-y-4">
          {pendingCollections.map((pending, index) => (
            <div key={index} className="flex justify-between items-center">
              <div>
                <div className="font-medium">{pending.tenant.fullName}</div>
                <div className="text-sm text-gray-500">{pending.building} - {pending.floor}</div>
              </div>
              <div className="text-right">
                <div className="text-red-600 font-medium">₹{pending.amount.toLocaleString()}</div>
                <div className="text-sm text-gray-500">+ ₹{pending.gstAmount.toLocaleString()} GST</div>
              </div>
            </div>
          ))}

          <div className="mt-6 pt-4 border-t">
            <div className="text-red-600 flex justify-between items-center">
              <span className="font-medium">Total Pending Rent:</span>
              <span className="font-bold">₹{stats.pendingRent.toLocaleString()}</span>
            </div>
            <div className="text-gray-600 flex justify-between items-center mt-1">
              <span>Total Pending GST:</span>
              <span>₹{stats.pendingGST.toLocaleString()}</span>
            </div>
            <div className="text-red-700 flex justify-between items-center mt-2 pt-2 border-t font-bold">
              <span>Total Pending:</span>
              <span>₹{(stats.pendingRent + stats.pendingGST).toLocaleString()}</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
