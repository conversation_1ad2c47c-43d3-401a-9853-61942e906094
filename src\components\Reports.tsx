import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { ArrowDownToLine, FileDown, Filter, TrendingUp, TrendingDown, DollarSign, Users, Building2, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getAllRentEntries, getAllTenants, type RentEntry, type Tenant } from '@/services/api';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface CollectionSummary {
  building: string;
  rent: number;
  gst: number;
  total: number;
}

interface PendingCollection {
  tenant: Tenant;
  amount: number;
  gstAmount: number;
  building: string;
  floor: string;
}

export const Reports = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [reportPeriod, setReportPeriod] = useState('This Month');
  const [selectedBuilding, setSelectedBuilding] = useState('All Buildings');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  // Summary statistics
  const [stats, setStats] = useState({
    rentCollected: 0,
    gstCollected: 0,
    pendingRent: 0,
    pendingGST: 0,
    totalDeposits: 0,
    totalTenants: 0
  });

  const [collectionSummary, setCollectionSummary] = useState<CollectionSummary[]>([]);
  const [pendingCollections, setPendingCollections] = useState<PendingCollection[]>([]);

  const buildings = ['All Buildings', 'Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const periods = ['This Month', 'This Quarter', 'This Year', 'Custom Range'];

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateSummary();
  }, [rentEntries, tenants, reportPeriod, selectedBuilding]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [entriesResponse, tenantsResponse] = await Promise.all([
        getAllRentEntries(),
        getAllTenants()
      ]);

      setRentEntries(entriesResponse.data || []);
      setTenants(tenantsResponse.data || []);
    } catch (error: any) {
      console.error('Error loading report data:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load report data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateSummary = () => {
    // Filter entries based on period and building
    const filteredEntries = rentEntries.filter(entry => {
      const entryDate = new Date(entry.rentMonth);
      const now = new Date();

      // Period filter
      let isInPeriod = false;
      switch (reportPeriod) {
        case 'This Month':
          isInPeriod = entryDate.getMonth() === now.getMonth() &&
            entryDate.getFullYear() === now.getFullYear();
          break;
        case 'This Quarter':
          const quarter = Math.floor(now.getMonth() / 3);
          const entryQuarter = Math.floor(entryDate.getMonth() / 3);
          isInPeriod = entryQuarter === quarter &&
            entryDate.getFullYear() === now.getFullYear();
          break;
        case 'This Year':
          isInPeriod = entryDate.getFullYear() === now.getFullYear();
          break;
        case 'Custom Range':
          if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            isInPeriod = entryDate >= start && entryDate <= end;
          } else {
            isInPeriod = true;
          }
          break;
        default:
          isInPeriod = true;
      }

      // Building filter
      const isBuildingMatch = selectedBuilding === 'All Buildings' ||
        entry.building === selectedBuilding;

      return isInPeriod && isBuildingMatch;
    });

    // Calculate collection summary by building
    const summary = filteredEntries.reduce((acc, entry) => {
      const building = entry.building;
      if (!acc[building]) {
        acc[building] = { building, rent: 0, gst: 0, total: 0 };
      }
      acc[building].rent += entry.rentDetails.totalRent;
      acc[building].gst += entry.rentDetails.gstAmount;
      acc[building].total += entry.rentDetails.totalPayable;
      return acc;
    }, {} as Record<string, CollectionSummary>);

    // Calculate pending collections based on the selected period
    const pending = tenants
      .filter(tenant => selectedBuilding === 'All Buildings' || tenant.building === selectedBuilding)
      .map(tenant => {
        // Get all entries for this tenant in the selected period
        const tenantEntries = filteredEntries
          .filter(e => e.tenant === tenant.id && e.paymentStatus === 'pending')
          .sort((a, b) => new Date(b.rentMonth).getTime() - new Date(a.rentMonth).getTime());

        if (tenantEntries.length === 0) return null;

        // Calculate total pending amount for this tenant
        const totalPending = tenantEntries.reduce((sum, entry) => sum + entry.rentDetails.totalRent, 0);
        const totalPendingGST = tenantEntries.reduce((sum, entry) => sum + entry.rentDetails.gstAmount, 0);

        return {
          tenant,
          amount: totalPending,
          gstAmount: totalPendingGST,
          building: tenant.building,
          floor: tenant.floor
        };
      })
      .filter((p): p is PendingCollection => p !== null && (p.amount > 0 || p.gstAmount > 0));

    // Calculate overall statistics
    const stats = {
      rentCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.totalRent : 0), 0),
      gstCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.gstAmount : 0), 0),
      pendingRent: pending.reduce((sum, p) => sum + p.amount, 0),
      pendingGST: pending.reduce((sum, p) => sum + p.gstAmount, 0),
      totalDeposits: tenants.reduce((sum, tenant) => sum + tenant.deposit, 0),
      totalTenants: tenants.length
    };

    setCollectionSummary(Object.values(summary));
    setPendingCollections(pending);
    setStats(stats);
  };

  const handleExportPDF = () => {
    // Implement PDF export functionality
    toast({
      title: "Coming Soon",
      description: "PDF export functionality will be available soon",
    });
  };

  const handleExportExcel = () => {
    // Implement Excel export functionality
    toast({
      title: "Coming Soon",
      description: "Excel export functionality will be available soon",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
      </div>

      {/* Filters */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
          <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
            <Filter className="h-5 w-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="space-y-2">
              <Label htmlFor="report-period" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Report Period</Label>
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger id="report-period" className="h-11">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map(period => (
                    <SelectItem key={period} value={period}>{period}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="building" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Building</Label>
              <Select value={selectedBuilding} onValueChange={setSelectedBuilding}>
                <SelectTrigger id="building" className="h-11">
                  <SelectValue placeholder="Select building" />
                </SelectTrigger>
                <SelectContent>
                  {buildings.map(building => (
                    <SelectItem key={building} value={building}>{building}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {reportPeriod === 'Custom Range' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="start-date" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Start Date</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="h-11"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date" className="text-sm font-semibold text-gray-700 dark:text-gray-300">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="h-11"
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex flex-wrap gap-3">
            <Button className="bg-blue-600 hover:bg-blue-700 h-11 px-6">
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleExportPDF} className="h-11 px-6">
              <FileDown className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" onClick={handleExportExcel} className="h-11 px-6">
              <ArrowDownToLine className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700 dark:text-green-300 mb-2">Rent Collected</p>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">₹{stats.rentCollected.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-800 flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950 dark:to-cyan-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">GST Collected</p>
                <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">₹{stats.gstCollected.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-red-50 to-rose-50 dark:from-red-950 dark:to-rose-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-700 dark:text-red-300 mb-2">Pending Rent</p>
                <p className="text-3xl font-bold text-red-600 dark:text-red-400">₹{stats.pendingRent.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-red-100 dark:bg-red-800 flex items-center justify-center">
                <TrendingDown className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-950 dark:to-amber-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">Pending GST</p>
                <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">₹{stats.pendingGST.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-orange-100 dark:bg-orange-800 flex items-center justify-center">
                <Calendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950 dark:to-violet-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">Total Deposits</p>
                <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">₹{stats.totalDeposits.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-100 dark:bg-purple-800 flex items-center justify-center">
                <Building2 className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-950 dark:to-slate-950">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Total Tenants</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">{stats.totalTenants}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                <Users className="h-6 w-6 text-gray-600 dark:text-gray-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Collection Summary */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-950 dark:to-gray-950">
          <CardTitle className="flex items-center gap-2 text-slate-700 dark:text-slate-300">
            <Building2 className="h-5 w-5" />
            Monthly Collection Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-900">
                  <th className="text-left py-4 px-6 font-semibold text-slate-700 dark:text-slate-300 text-sm uppercase tracking-wider">Building</th>
                  <th className="text-right py-4 px-6 font-semibold text-slate-700 dark:text-slate-300 text-sm uppercase tracking-wider">Rent</th>
                  <th className="text-right py-4 px-6 font-semibold text-slate-700 dark:text-slate-300 text-sm uppercase tracking-wider">GST</th>
                  <th className="text-right py-4 px-6 font-semibold text-slate-700 dark:text-slate-300 text-sm uppercase tracking-wider">Total</th>
                </tr>
              </thead>
              <tbody>
                {collectionSummary.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="text-center py-12 text-muted-foreground">
                      <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No collection data found for the selected period</p>
                    </td>
                  </tr>
                ) : (
                  collectionSummary.map((summary, index) => (
                    <tr key={index} className="border-b border-slate-100 dark:border-slate-800 hover:bg-slate-50 dark:hover:bg-slate-900/50 transition-colors">
                      <td className="py-4 px-6 font-medium text-slate-900 dark:text-slate-100">{summary.building}</td>
                      <td className="py-4 px-6 text-right text-green-600 dark:text-green-400 font-semibold">₹{summary.rent.toLocaleString()}</td>
                      <td className="py-4 px-6 text-right text-blue-600 dark:text-blue-400 font-semibold">₹{summary.gst.toLocaleString()}</td>
                      <td className="py-4 px-6 text-right font-bold text-slate-900 dark:text-slate-100">₹{summary.total.toLocaleString()}</td>
                    </tr>
                  ))
                )}
                {collectionSummary.length > 0 && (
                  <tr className="border-t-2 border-slate-300 dark:border-slate-600 bg-gradient-to-r from-slate-100 to-gray-100 dark:from-slate-800 dark:to-gray-800">
                    <td className="py-5 px-6 font-bold text-lg text-slate-900 dark:text-slate-100">Grand Total</td>
                    <td className="py-5 px-6 text-right font-bold text-lg text-green-600 dark:text-green-400">₹{stats.rentCollected.toLocaleString()}</td>
                    <td className="py-5 px-6 text-right font-bold text-lg text-blue-600 dark:text-blue-400">₹{stats.gstCollected.toLocaleString()}</td>
                    <td className="py-5 px-6 text-right font-bold text-xl text-slate-900 dark:text-slate-100">₹{(stats.rentCollected + stats.gstCollected).toLocaleString()}</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pending Collections */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-950 dark:to-rose-950">
          <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-300">
            <TrendingDown className="h-5 w-5" />
            Pending Collections
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {pendingCollections.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <div className="h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 opacity-50" />
                </div>
                <p className="text-lg font-medium">No pending collections found</p>
                <p className="text-sm">All rent payments are up to date!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {pendingCollections.map((pending, index) => (
                  <div key={index} className="flex items-center justify-between p-5 border-0 rounded-xl bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/50 dark:to-orange-950/50 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center gap-4">
                      <div className="h-12 w-12 rounded-full bg-red-100 dark:bg-red-800 flex items-center justify-center">
                        <Users className="h-6 w-6 text-red-600 dark:text-red-400" />
                      </div>
                      <div>
                        <div className="font-semibold text-slate-900 dark:text-slate-100">{pending.tenant.fullName}</div>
                        <div className="text-sm text-slate-600 dark:text-slate-400 flex items-center gap-1">
                          <Building2 className="h-3 w-3" />
                          {pending.building} - {pending.floor}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-red-600 dark:text-red-400 font-bold text-lg">₹{pending.amount.toLocaleString()}</div>
                      <div className="text-sm text-orange-600 dark:text-orange-400 font-medium">+ ₹{pending.gstAmount.toLocaleString()} GST</div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {pendingCollections.length > 0 && (
              <div className="mt-8 pt-6 border-t-2 border-slate-200 dark:border-slate-700">
                <div className="bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900 dark:to-gray-900 rounded-xl p-6 space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-slate-700 dark:text-slate-300">Total Pending Rent:</span>
                    <span className="font-bold text-xl text-red-600 dark:text-red-400">₹{stats.pendingRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-slate-700 dark:text-slate-300">Total Pending GST:</span>
                    <span className="font-bold text-xl text-orange-600 dark:text-orange-400">₹{stats.pendingGST.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center pt-4 border-t border-slate-200 dark:border-slate-700">
                    <span className="font-bold text-xl text-slate-900 dark:text-slate-100">Total Pending:</span>
                    <span className="font-bold text-2xl text-red-700 dark:text-red-400">₹{(stats.pendingRent + stats.pendingGST).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
    </div >
  );
};
