import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { ArrowDownToLine, FileDown, Filter, TrendingUp, TrendingDown, DollarSign, Users, Building2, Calendar, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getAllRentEntries, getAllTenants, type RentEntry } from '@/services/api';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';

interface CollectionSummary {
  building: string;
  rent: number;
  gst: number;
  total: number;
  roomRent: number;
}

interface Tenant {
  id?: string;
  fullName: string;
  firmName: string;
  phone: string;
  email: string;
  address: string;
  building: string;
  floor: string;
  rentDetails: {
    cashRent: number;
    bankRent: number;
    roomRent: number;
    gst: number;
    gstAmount: number;
    totalRent: number;
    totalPayable: number;
  };
  deposit: number;
  rentStartDate: string;
  nextIncrementDate: string;
  notes: string;
  isActive?: boolean;
}

interface PendingCollection {
  tenant: Tenant;
  cashRent: number;
  bankRent: number;
  roomRentAmount: number;
  gstAmount: number;
  totalAmount: number;
  building: string;
  floor: string;
  pendingMonths?: number;
  monthlyCashRent?: number;
  monthlyBankRent?: number;
  monthlyRoomRent?: number;
  monthlyGST?: number;
  monthlyTotal?: number;
}

export const Reports = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [reportPeriod, setReportPeriod] = useState('This Month');
  const [selectedBuilding, setSelectedBuilding] = useState('All Buildings');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  // Summary statistics
  const [stats, setStats] = useState({
    rentCollected: 0,
    gstCollected: 0,
    roomRentCollected: 0,
    pendingCashRent: 0,
    pendingBankRent: 0,
    pendingRoomRent: 0,
    pendingGST: 0,
    totalDeposits: 0,
    totalTenants: 0
  });

  const [collectionSummary, setCollectionSummary] = useState<CollectionSummary[]>([]);
  const [pendingCollections, setPendingCollections] = useState<PendingCollection[]>([]);

  const buildings = ['All Buildings', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const periods = ['This Month', 'This Quarter', 'This Year', 'Custom Range'];

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateSummary();
  }, [rentEntries, tenants, reportPeriod, selectedBuilding, startDate, endDate]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [entriesResponse, tenantsResponse] = await Promise.all([
        getAllRentEntries(),
        getAllTenants()
      ]);

      setRentEntries(entriesResponse.data || []);
      setTenants(tenantsResponse.data || []);
    } catch (error: any) {
      console.error('Error loading report data:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load report data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get date range based on selected period
  const getDateRange = () => {
    const now = new Date();
    let periodStartDate: Date, periodEndDate: Date;

    switch (reportPeriod) {
      case 'This Month':
        periodStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
        periodEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      case 'This Quarter':
        const quarter = Math.floor(now.getMonth() / 3);
        periodStartDate = new Date(now.getFullYear(), quarter * 3, 1);
        periodEndDate = new Date(now.getFullYear(), quarter * 3 + 3, 0);
        break;
      case 'This Year':
        periodStartDate = new Date(now.getFullYear(), 0, 1);
        periodEndDate = new Date(now.getFullYear(), 11, 31);
        break;
      case 'Custom Range':
        if (startDate && endDate) {
          periodStartDate = new Date(startDate);
          periodEndDate = new Date(endDate);
        } else {
          periodStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
          periodEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        }
        break;
      default:
        periodStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
        periodEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    }

    // Set time to start and end of day
    periodStartDate.setHours(0, 0, 0, 0);
    periodEndDate.setHours(23, 59, 59, 999);

    return { startDate: periodStartDate, endDate: periodEndDate };
  };

  // Helper function to get expected rent months for a tenant in the selected period
  const getExpectedRentMonths = (tenant: Tenant, periodStart: Date, periodEnd: Date) => {
    const months = [];
    const rentStart = new Date(tenant.rentStartDate);

    // Set rentStart to the first day of its month
    rentStart.setDate(1);
    rentStart.setHours(0, 0, 0, 0);

    // Use the later of rentStart and periodStart as the starting point
    const current = new Date(Math.max(rentStart.getTime(), periodStart.getTime()));
    current.setDate(1); // Ensure we're at the start of the month

    const end = new Date(periodEnd);
    end.setDate(1); // Set to first of month for comparison

    // Don't include future months in the calculation
    const today = new Date();
    today.setDate(1);
    today.setHours(0, 0, 0, 0);
    const endDate = end > today ? today : end;

    while (current <= endDate) {
      months.push(new Date(current));
      current.setMonth(current.getMonth() + 1);
    }

    return months;
  };

  // Helper function to normalize date to YYYY-MM format for comparison
  const normalizeDate = (date: Date | string) => {
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
  };

  const calculateSummary = () => {
    const { startDate: periodStart, endDate: periodEnd } = getDateRange();

    // Filter entries based on period and building
    const filteredEntries = rentEntries.filter(entry => {
      const entryDate = new Date(entry.rentMonth);
      entryDate.setHours(0, 0, 0, 0); // Normalize time to start of day

      // Period filter
      const isInPeriod = entryDate >= periodStart && entryDate <= periodEnd;

      // Building filter
      const isBuildingMatch = selectedBuilding === 'All Buildings' ||
        entry.building === selectedBuilding;

      return isInPeriod && isBuildingMatch;
    });

    // Calculate collection summary by building
    const summary = filteredEntries.reduce((acc, entry) => {
      const building = entry.building;
      if (!acc[building]) {
        acc[building] = { building, rent: 0, gst: 0, total: 0, roomRent: 0 };
      }
      if (entry.paymentStatus === 'paid') {
        acc[building].rent += entry.rentDetails.totalRent;
        acc[building].gst += entry.rentDetails.gstAmount;
        acc[building].roomRent += entry.rentDetails.roomRent || 0;
        acc[building].total += entry.rentDetails.totalPayable + (entry.rentDetails.roomRent || 0);
      }
      return acc;
    }, {} as Record<string, CollectionSummary>);

    // Calculate pending collections based on the selected period
    const pending = tenants
      .filter(tenant => {
        // Filter by building
        if (selectedBuilding !== 'All Buildings' && tenant.building !== selectedBuilding) {
          return false;
        }

        // Only include active tenants
        if (tenant.isActive === false) {
          return false;
        }

        // Check if tenant's rent start date is within or before the period
        const rentStart = new Date(tenant.rentStartDate);
        rentStart.setHours(0, 0, 0, 0);
        return rentStart <= periodEnd;
      })
      .map(tenant => {
        // Get expected rent months for this tenant in the period
        const expectedMonths = getExpectedRentMonths(tenant, periodStart, periodEnd);

        // Get all paid rent entries for this tenant
        const tenantEntries = rentEntries.filter(entry => {
          let entryTenantId: string;
          if (typeof entry.tenant === 'string') {
            entryTenantId = entry.tenant;
          } else if (entry.tenant && typeof entry.tenant === 'object') {
            entryTenantId = entry.tenant.id || (entry.tenant as any)._id;
          } else {
            return false;
          }

          const tenantId = tenant.id || (tenant as any)._id;
          return entryTenantId === tenantId && entry.paymentStatus === 'paid';
        });

        // Find missing or pending rent entries
        const pendingMonths = expectedMonths.filter(expectedMonth => {
          const expectedMonthStr = normalizeDate(expectedMonth);

          // Check if there's a paid rent entry for this month
          const entryForMonth = tenantEntries.find(entry => {
            const entryMonth = normalizeDate(entry.rentMonth);
            return entryMonth === expectedMonthStr;
          });

          // Consider it pending if no paid entry exists for this month
          return !entryForMonth;
        });

        if (pendingMonths.length === 0) return null;

        // Calculate total pending amount
        const monthlyCashRent = tenant.rentDetails.cashRent;
        const monthlyBankRent = tenant.rentDetails.bankRent;
        const monthlyRoomRent = tenant.rentDetails.roomRent || 0;
        const monthlyGST = tenant.rentDetails.gstAmount;
        const monthlyTotal = tenant.rentDetails.totalPayable;

        const totalPendingCashRent = monthlyCashRent * pendingMonths.length;
        const totalPendingBankRent = monthlyBankRent * pendingMonths.length;
        const totalPendingRoomRent = monthlyRoomRent * pendingMonths.length;
        const totalPendingGST = monthlyGST * pendingMonths.length;

        return {
          tenant,
          cashRent: totalPendingCashRent,
          bankRent: totalPendingBankRent,
          roomRentAmount: totalPendingRoomRent,
          gstAmount: totalPendingGST,
          totalAmount: monthlyTotal * pendingMonths.length,
          building: tenant.building,
          floor: tenant.floor,
          pendingMonths: pendingMonths.length,
          monthlyCashRent,
          monthlyBankRent,
          monthlyRoomRent,
          monthlyGST,
          monthlyTotal
        };
      })
      .filter((p): p is NonNullable<typeof p> => p !== null);

    // Calculate overall statistics
    const stats = {
      rentCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.totalRent : 0), 0),
      gstCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.gstAmount : 0), 0),
      roomRentCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? (entry.rentDetails.roomRent || 0) : 0), 0),
      pendingCashRent: pending.reduce((sum, p) => sum + p.cashRent, 0),
      pendingBankRent: pending.reduce((sum, p) => sum + p.bankRent, 0),
      pendingRoomRent: pending.reduce((sum, p) => sum + p.roomRentAmount, 0),
      pendingGST: pending.reduce((sum, p) => sum + p.gstAmount, 0),
      totalDeposits: tenants.reduce((sum, tenant) => sum + tenant.deposit, 0),
      totalTenants: tenants.length
    };

    setCollectionSummary(Object.values(summary));
    setPendingCollections(pending);
    setStats(stats);
  };

  const handleExportPDF = () => {
    try {
      const doc = new jsPDF();
      const pageWidth = doc.internal.pageSize.width;
      const currentDate = new Date().toLocaleDateString();

      // Add Header
      doc.setFontSize(20);
      doc.setTextColor(44, 62, 80);
      doc.text('Rent Guardian', pageWidth / 2, 15, { align: 'center' });

      doc.setFontSize(16);
      doc.text('Rent Collection Report', pageWidth / 2, 25, { align: 'center' });

      // Add Report Info
      doc.setFontSize(11);
      doc.setTextColor(100, 100, 100);
      doc.text(`Generated on: ${currentDate}`, 15, 35);
      doc.text(`Period: ${reportPeriod}`, 15, 42);
      doc.text(`Building: ${selectedBuilding}`, 15, 49);

      // Add Collection Statistics
      doc.setFontSize(14);
      doc.setTextColor(44, 62, 80);
      doc.text('Collection Statistics', 15, 60);

      const collectionStats = [
        ['Description', 'Amount'],
        ['Rent Collected', `₹${stats.rentCollected.toLocaleString()}`],
        ['Room Rent Collected', `₹${stats.roomRentCollected.toLocaleString()}`],
        ['GST Collected', `₹${stats.gstCollected.toLocaleString()}`],
        ['Total Collection', `₹${(stats.rentCollected + stats.roomRentCollected + stats.gstCollected).toLocaleString()}`],
      ];

      autoTable(doc, {
        startY: 65,
        head: [collectionStats[0]],
        body: collectionStats.slice(1),
        theme: 'striped',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontSize: 12,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 11,
          cellPadding: 5
        },
        columnStyles: {
          0: { fontStyle: 'bold' }
        }
      });

      // Get the Y position after the last table
      const lastTableY = (doc as any).lastAutoTable?.finalY || 120;

      // Add Pending Statistics
      doc.setFontSize(14);
      doc.setTextColor(44, 62, 80);
      doc.text('Pending Statistics', 15, lastTableY + 15);

      const pendingStats = [
        ['Description', 'Amount'],
        ['Pending Cash Rent', `₹${stats.pendingCashRent.toLocaleString()}`],
        ['Pending Bank Rent', `₹${stats.pendingBankRent.toLocaleString()}`],
        ['Pending Room Rent', `₹${stats.pendingRoomRent.toLocaleString()}`],
        ['Pending GST', `₹${stats.pendingGST.toLocaleString()}`],
        ['Total Pending', `₹${(stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST).toLocaleString()}`],
      ];

      autoTable(doc, {
        startY: lastTableY + 20,
        head: [pendingStats[0]],
        body: pendingStats.slice(1),
        theme: 'striped',
        headStyles: {
          fillColor: [192, 57, 43],
          textColor: 255,
          fontSize: 12,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 11,
          cellPadding: 5
        },
        columnStyles: {
          0: { fontStyle: 'bold' }
        }
      });

      // Add Building-wise Collection Details
      doc.addPage();
      doc.setFontSize(16);
      doc.setTextColor(44, 62, 80);
      doc.text('Building-wise Collection Details', pageWidth / 2, 15, { align: 'center' });

      const buildingData = collectionSummary.map(summary => [
        summary.building,
        `₹${summary.rent.toLocaleString()}`,
        `₹${summary.roomRent.toLocaleString()}`,
        `₹${summary.gst.toLocaleString()}`,
        `₹${summary.total.toLocaleString()}`
      ]);

      autoTable(doc, {
        startY: 25,
        head: [['Building', 'Rent', 'Room Rent', 'GST', 'Total']],
        body: buildingData,
        theme: 'striped',
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontSize: 12,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 11,
          cellPadding: 5
        }
      });

      // Add Pending Collections
      if (pendingCollections.length > 0) {
        doc.addPage();
        doc.setFontSize(16);
        doc.setTextColor(44, 62, 80);
        doc.text('Pending Collections Details', pageWidth / 2, 15, { align: 'center' });

        const pendingData = pendingCollections.map(pending => [
          pending.tenant.fullName,
          pending.building,
          pending.floor,
          `₹${(pending.cashRent + pending.bankRent + pending.roomRentAmount + pending.gstAmount).toLocaleString()}`,
          `${pending.pendingMonths} month(s)`,
          `₹${((pending.monthlyCashRent || 0) + (pending.monthlyBankRent || 0) + (pending.monthlyRoomRent || 0) + (pending.monthlyGST || 0)).toLocaleString()}/month`,
          pending.tenant.phone || 'N/A'
        ]);

        autoTable(doc, {
          startY: 25,
          head: [['Tenant Name', 'Building', 'Floor', 'Total Pending', 'Duration', 'Monthly Amount', 'Contact']],
          body: pendingData,
          theme: 'striped',
          headStyles: {
            fillColor: [192, 57, 43],
            textColor: 255,
            fontSize: 10,
            fontStyle: 'bold'
          },
          styles: {
            fontSize: 9,
            cellPadding: 4,
            overflow: 'linebreak'
          },
          columnStyles: {
            0: { fontStyle: 'bold', cellWidth: 'auto' },
            1: { cellWidth: 'auto' },
            2: { cellWidth: 'auto' },
            3: { halign: 'right', cellWidth: 'auto' },
            4: { halign: 'center', cellWidth: 'auto' },
            5: { halign: 'right', cellWidth: 'auto' },
            6: { cellWidth: 'auto' }
          },
          tableWidth: 'auto',
          margin: { left: 10, right: 10 }
        });

        // Add detailed breakdown on a separate page
        doc.addPage();
        doc.setFontSize(16);
        doc.setTextColor(44, 62, 80);
        doc.text('Detailed Pending Collections Breakdown', pageWidth / 2, 15, { align: 'center' });

        const detailedPendingData = pendingCollections.map(pending => [
          pending.tenant.fullName,
          pending.building,
          `₹${pending.cashRent.toLocaleString()}`,
          `₹${pending.bankRent.toLocaleString()}`,
          `₹${pending.roomRentAmount.toLocaleString()}`,
          `₹${pending.gstAmount.toLocaleString()}`,
          `₹${(pending.cashRent + pending.bankRent + pending.roomRentAmount + pending.gstAmount).toLocaleString()}`,
          `${pending.pendingMonths} month(s)`,
          pending.tenant.email || 'N/A'
        ]);

        autoTable(doc, {
          startY: 25,
          head: [['Tenant', 'Building', 'Pending Rent', 'Pending Room Rent', 'Pending GST', 'Total Pending', 'Duration', 'Email']],
          body: detailedPendingData,
          theme: 'striped',
          headStyles: {
            fillColor: [192, 57, 43],
            textColor: 255,
            fontSize: 9,
            fontStyle: 'bold'
          },
          styles: {
            fontSize: 8,
            cellPadding: 3,
            overflow: 'linebreak'
          },
          columnStyles: {
            0: { fontStyle: 'bold', cellWidth: 'auto' },
            1: { cellWidth: 'auto' },
            2: { halign: 'right', cellWidth: 'auto' },
            3: { halign: 'right', cellWidth: 'auto' },
            4: { halign: 'right', cellWidth: 'auto' },
            5: { halign: 'right', cellWidth: 'auto' },
            6: { halign: 'center', cellWidth: 'auto' },
            7: { cellWidth: 'auto' }
          },
          tableWidth: 'auto',
          margin: { left: 10, right: 10 }
        });

        // Add summary of pending collections
        doc.addPage();
        doc.setFontSize(16);
        doc.setTextColor(44, 62, 80);
        doc.text('Pending Collections Summary', pageWidth / 2, 15, { align: 'center' });

        const pendingSummaryData = [
          ['Description', 'Amount'],
          ['Total Pending Cash Rent', `₹${stats.pendingCashRent.toLocaleString()}`],
          ['Total Pending Bank Rent', `₹${stats.pendingBankRent.toLocaleString()}`],
          ['Total Pending Room Rent', `₹${stats.pendingRoomRent.toLocaleString()}`],
          ['Total Pending GST', `₹${stats.pendingGST.toLocaleString()}`],
          ['Total Pending Amount', `₹${(stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST).toLocaleString()}`],
          ['Total Pending Tenants', pendingCollections.length.toString()],
          ['Average Pending Duration', `${Math.round(pendingCollections.reduce((sum, p) => sum + (p.pendingMonths || 0), 0) / pendingCollections.length)} month(s)`]
        ];

        autoTable(doc, {
          startY: 25,
          head: [pendingSummaryData[0]],
          body: pendingSummaryData.slice(1),
          theme: 'striped',
          headStyles: {
            fillColor: [192, 57, 43],
            textColor: 255,
            fontSize: 12,
            fontStyle: 'bold'
          },
          styles: {
            fontSize: 11,
            cellPadding: 5
          },
          columnStyles: {
            0: { fontStyle: 'bold' },
            1: { halign: 'right' }
          }
        });
      }

      // Add footer with generation info
      const pageCount = (doc as any).internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.setTextColor(128, 128, 128);
        doc.text(
          `Generated on: ${currentDate} | Page ${i} of ${pageCount}`,
          pageWidth / 2,
          doc.internal.pageSize.height - 10,
          { align: 'center' }
        );
      }

      // Save the PDF with date in filename
      doc.save(`rent-collection-report-${currentDate.replace(/\//g, '-')}.pdf`);
    } catch (error) {
      console.error('PDF Export Error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleExportExcel = () => {
    try {
      const wb = XLSX.utils.book_new();
      const currentDate = new Date().toLocaleDateString();

      // Summary Sheet with comprehensive data
      const summaryData = [
        ['Rent Guardian - Collection Report'],
        [''],
        ['Report Information'],
        ['Generated Date', currentDate],
        ['Period', reportPeriod],
        ['Building', selectedBuilding],
        ['Start Date', reportPeriod === 'Custom Range' ? startDate : 'N/A'],
        ['End Date', reportPeriod === 'Custom Range' ? endDate : 'N/A'],
        [''],
        ['Collection Statistics'],
        ['Description', 'Amount (₹)'],
        ['Rent Collected', stats.rentCollected],
        ['Room Rent Collected', stats.roomRentCollected],
        ['GST Collected', stats.gstCollected],
        ['Total Collection', stats.rentCollected + stats.roomRentCollected + stats.gstCollected],
        [''],
        ['Pending Statistics'],
        ['Description', 'Amount (₹)'],
        ['Pending Cash Rent', stats.pendingCashRent],
        ['Pending Bank Rent', stats.pendingBankRent],
        ['Pending Room Rent', stats.pendingRoomRent],
        ['Pending GST', stats.pendingGST],
        ['Total Pending', stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST],
        [''],
        ['Other Statistics'],
        ['Description', 'Value'],
        ['Total Deposits', stats.totalDeposits],
        ['Total Tenants', stats.totalTenants],
        ['Pending Tenants Count', pendingCollections.length],
        ['Collection Rate (%)', pendingCollections.length > 0 ? Math.round(((stats.totalTenants - pendingCollections.length) / stats.totalTenants) * 100) : 100],
        ['Average Pending Duration (months)', pendingCollections.length > 0 ? Math.round(pendingCollections.reduce((sum, p) => sum + (p.pendingMonths || 0), 0) / pendingCollections.length) : 0],
        [''],
        ['Financial Summary'],
        ['Total Revenue (Collections + Deposits)', stats.rentCollected + stats.roomRentCollected + stats.gstCollected + stats.totalDeposits],
        ['Outstanding Amount', stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST],
        ['Net Collection Efficiency (%)', Math.round(((stats.rentCollected + stats.roomRentCollected + stats.gstCollected) / (stats.rentCollected + stats.roomRentCollected + stats.gstCollected + stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST)) * 100)]
      ];

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);

      // Apply formatting to summary sheet
      const summaryRange = XLSX.utils.decode_range(summarySheet['!ref'] || 'A1');
      for (let R = summaryRange.s.r; R <= summaryRange.e.r; ++R) {
        for (let C = summaryRange.s.c; C <= summaryRange.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (!summarySheet[cellAddress]) continue;

          // Style headers and important rows
          if (R === 0 || summaryData[R][0] === 'Collection Statistics' || summaryData[R][0] === 'Pending Statistics' || summaryData[R][0] === 'Other Statistics' || summaryData[R][0] === 'Financial Summary') {
            summarySheet[cellAddress].s = {
              font: { bold: true, sz: 12 },
              fill: { fgColor: { rgb: "E3F2FD" } }
            };
          }
        }
      }

      XLSX.utils.book_append_sheet(wb, summarySheet, 'Summary');

      // Building Collections Sheet with enhanced details
      const buildingData = [
        ['Building-wise Collection Details'],
        ['Generated on:', currentDate],
        ['Report Period:', reportPeriod],
        [''],
        ['Building', 'Rent Collected (₹)', 'Room Rent Collected (₹)', 'GST Collected (₹)', 'Total Collected (₹)', 'Collection Period', 'Building Status', 'Collection %'],
        ...collectionSummary.map(summary => {
          const totalPossible = summary.total + (stats.pendingCashRent + stats.pendingBankRent + stats.pendingGST) / collectionSummary.length;
          const collectionPercentage = totalPossible > 0 ? Math.round((summary.total / totalPossible) * 100) : 100;
          return [
            summary.building,
            summary.rent,
            summary.roomRent,
            summary.gst,
            summary.total,
            reportPeriod,
            'Active',
            `${collectionPercentage}%`
          ];
        }),
        [''],
        ['Summary'],
        ['Total Rent Collected', stats.rentCollected],
        ['Total Room Rent Collected', stats.roomRentCollected],
        ['Total GST Collected', stats.gstCollected],
        ['Grand Total Collected', stats.rentCollected + stats.roomRentCollected + stats.gstCollected],
        ['Number of Buildings', collectionSummary.length]
      ];
      const buildingSheet = XLSX.utils.aoa_to_sheet(buildingData);

      // Apply formatting to building sheet
      const buildingRange = XLSX.utils.decode_range(buildingSheet['!ref'] || 'A1');
      for (let R = buildingRange.s.r; R <= buildingRange.e.r; ++R) {
        for (let C = buildingRange.s.c; C <= buildingRange.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (!buildingSheet[cellAddress]) continue;

          // Style headers
          if (R === 0 || R === 4 || buildingData[R][0] === 'Summary') {
            buildingSheet[cellAddress].s = {
              font: { bold: true, sz: 11 },
              fill: { fgColor: { rgb: "F3E5F5" } }
            };
          }
        }
      }

      XLSX.utils.book_append_sheet(wb, buildingSheet, 'Building Collections');

      // Pending Collections Sheet with enhanced details
      if (pendingCollections.length > 0) {
        const pendingData = [
          ['Pending Collections Details'],
          ['Generated on:', currentDate],
          ['Report Period:', reportPeriod],
          ['Total Pending Tenants:', pendingCollections.length],
          [''],
          [
            'Tenant Name',
            'Building',
            'Floor',
            'Pending Rent (₹)',
            'Pending Room Rent (₹)',
            'Pending GST (₹)',
            'Total Pending (₹)',
            'Pending Duration (Months)',
            'Monthly Rent (₹)',
            'Monthly Room Rent (₹)',
            'Monthly GST (₹)',
            'Total Monthly (₹)',
            'Contact Number',
            'Email',
            'Rent Start Date',
            'Last Payment Date',
            'Priority Level'
          ],
          ...pendingCollections.map(pending => {
            const totalPending = pending.cashRent + pending.bankRent + pending.roomRentAmount + pending.gstAmount;
            const priorityLevel = pending.pendingMonths >= 3 ? 'High' : pending.pendingMonths >= 2 ? 'Medium' : 'Low';
            return [
              pending.tenant.fullName,
              pending.building,
              pending.floor,
              pending.cashRent,
              pending.roomRentAmount,
              pending.gstAmount,
              totalPending,
              pending.pendingMonths,
              pending.monthlyCashRent || 0,
              pending.monthlyRoomRent || 0,
              pending.monthlyGST || 0,
              (pending.monthlyCashRent || 0) + (pending.monthlyRoomRent || 0) + (pending.monthlyGST || 0),
              pending.tenant.phone || 'N/A',
              pending.tenant.email || 'N/A',
              pending.tenant.rentStartDate || 'N/A',
              'N/A', // Last payment date - would need to be added to data
              priorityLevel
            ];
          }),
          [''],
          ['Pending Collections Summary'],
          ['Total Pending Rent', stats.pendingCashRent],
          ['Total Pending Room Rent', stats.pendingRoomRent],
          ['Total Pending GST', stats.pendingGST],
          ['Grand Total Pending', stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST],
          ['Average Pending per Tenant', Math.round((stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST) / pendingCollections.length)],
          ['High Priority Cases (3+ months)', pendingCollections.filter(p => p.pendingMonths >= 3).length],
          ['Medium Priority Cases (2 months)', pendingCollections.filter(p => p.pendingMonths === 2).length],
          ['Low Priority Cases (1 month)', pendingCollections.filter(p => p.pendingMonths === 1).length]
        ];

        const pendingSheet = XLSX.utils.aoa_to_sheet(pendingData);

        // Apply formatting to pending sheet
        const pendingRange = XLSX.utils.decode_range(pendingSheet['!ref'] || 'A1');
        for (let R = pendingRange.s.r; R <= pendingRange.e.r; ++R) {
          for (let C = pendingRange.s.c; C <= pendingRange.e.c; ++C) {
            const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
            if (!pendingSheet[cellAddress]) continue;

            // Style headers and summary sections
            if (R === 0 || R === 5 || pendingData[R][0] === 'Pending Collections Summary') {
              pendingSheet[cellAddress].s = {
                font: { bold: true, sz: 11 },
                fill: { fgColor: { rgb: "FFEBEE" } }
              };
            }

            // Highlight high priority cases
            if (R > 5 && R < pendingData.length - 8 && pendingData[R][16] === 'High') {
              pendingSheet[cellAddress].s = {
                fill: { fgColor: { rgb: "FFCDD2" } }
              };
            }
          }
        }

        XLSX.utils.book_append_sheet(wb, pendingSheet, 'Pending Collections');
      } else {
        // Create empty pending collections sheet with message
        const noPendingData = [
          ['Pending Collections Details'],
          ['Generated on:', currentDate],
          ['Report Period:', reportPeriod],
          [''],
          ['No pending collections found for the selected period.'],
          ['All tenants are up to date with their payments!']
        ];
        const noPendingSheet = XLSX.utils.aoa_to_sheet(noPendingData);
        XLSX.utils.book_append_sheet(wb, noPendingSheet, 'Pending Collections');
      }

      // Tenant Details Sheet
      const tenantDetailsData = [
        ['Complete Tenant Information'],
        ['Generated on:', currentDate],
        ['Report Period:', reportPeriod],
        [''],
        [
          'Tenant Name',
          'Building',
          'Floor',
          'Room Number',
          'Monthly Rent (₹)',
          'Monthly Room Rent (₹)',
          'Monthly GST (₹)',
          'Total Monthly (₹)',
          'Security Deposit (₹)',
          'Rent Start Date',
          'Contact Number',
          'Email',
          'Status',
          'Payment Status'
        ],
        // Add pending tenants data
        ...pendingCollections.map(pending => [
          pending.tenant.fullName,
          pending.building,
          pending.floor,
          'N/A', // Room number not available in current data structure
          pending.monthlyCashRent || 0,
          pending.monthlyRoomRent || 0,
          pending.monthlyGST || 0,
          (pending.monthlyCashRent || 0) + (pending.monthlyRoomRent || 0) + (pending.monthlyGST || 0),
          0, // Security deposit not available in current data structure
          pending.tenant.rentStartDate || 'N/A',
          pending.tenant.phone || 'N/A',
          pending.tenant.email || 'N/A',
          'Active',
          'Pending'
        ])
      ];

      const tenantSheet = XLSX.utils.aoa_to_sheet(tenantDetailsData);
      XLSX.utils.book_append_sheet(wb, tenantSheet, 'Tenant Details');

      // Financial Analysis Sheet
      const totalRevenue = stats.rentCollected + stats.roomRentCollected + stats.gstCollected + stats.totalDeposits;
      const totalOutstanding = stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST;
      const collectionEfficiency = totalRevenue > 0 ? Math.round(((totalRevenue - stats.totalDeposits) / (totalRevenue - stats.totalDeposits + totalOutstanding)) * 100) : 0;

      const financialAnalysisData = [
        ['Financial Analysis & KPIs'],
        ['Generated on:', currentDate],
        ['Report Period:', reportPeriod],
        [''],
        ['Revenue Analysis'],
        ['Description', 'Amount (₹)', 'Percentage of Total Revenue'],
        ['Rent Collections', stats.rentCollected, `${Math.round((stats.rentCollected / totalRevenue) * 100)}%`],
        ['Room Rent Collections', stats.roomRentCollected, `${Math.round((stats.roomRentCollected / totalRevenue) * 100)}%`],
        ['GST Collections', stats.gstCollected, `${Math.round((stats.gstCollected / totalRevenue) * 100)}%`],
        ['Security Deposits', stats.totalDeposits, `${Math.round((stats.totalDeposits / totalRevenue) * 100)}%`],
        ['Total Revenue', totalRevenue, '100%'],
        [''],
        ['Outstanding Analysis'],
        ['Description', 'Amount (₹)', 'Percentage of Total Outstanding'],
        ['Pending Cash Rent', stats.pendingCashRent, `${Math.round((stats.pendingCashRent / totalOutstanding) * 100)}%`],
        ['Pending Bank Rent', stats.pendingBankRent, `${Math.round((stats.pendingBankRent / totalOutstanding) * 100)}%`],
        ['Pending Room Rent', stats.pendingRoomRent, `${Math.round((stats.pendingRoomRent / totalOutstanding) * 100)}%`],
        ['Pending GST', stats.pendingGST, `${Math.round((stats.pendingGST / totalOutstanding) * 100)}%`],
        ['Total Outstanding', totalOutstanding, '100%'],
        [''],
        ['Key Performance Indicators'],
        ['Metric', 'Value', 'Status'],
        ['Collection Efficiency', `${collectionEfficiency}%`, collectionEfficiency >= 90 ? 'Excellent' : collectionEfficiency >= 80 ? 'Good' : collectionEfficiency >= 70 ? 'Average' : 'Needs Improvement'],
        ['Tenant Retention Rate', `${Math.round(((stats.totalTenants - pendingCollections.filter(p => p.pendingMonths >= 3).length) / stats.totalTenants) * 100)}%`, 'Good'],
        ['Average Revenue per Tenant', `₹${Math.round((stats.rentCollected + stats.roomRentCollected + stats.gstCollected) / stats.totalTenants)}`, 'N/A'],
        ['Average Outstanding per Pending Tenant', pendingCollections.length > 0 ? `₹${Math.round(totalOutstanding / pendingCollections.length)}` : '₹0', 'N/A'],
        ['Buildings with 100% Collection', collectionSummary.filter(s => s.total > 0).length, 'N/A'],
        ['High Risk Tenants (3+ months pending)', pendingCollections.filter(p => p.pendingMonths >= 3).length, pendingCollections.filter(p => p.pendingMonths >= 3).length === 0 ? 'Excellent' : 'Needs Attention']
      ];

      const financialSheet = XLSX.utils.aoa_to_sheet(financialAnalysisData);

      // Apply formatting to financial analysis sheet
      const financialRange = XLSX.utils.decode_range(financialSheet['!ref'] || 'A1');
      for (let R = financialRange.s.r; R <= financialRange.e.r; ++R) {
        for (let C = financialRange.s.c; C <= financialRange.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (!financialSheet[cellAddress]) continue;

          // Style headers and sections
          if (R === 0 || financialAnalysisData[R][0] === 'Revenue Analysis' || financialAnalysisData[R][0] === 'Outstanding Analysis' || financialAnalysisData[R][0] === 'Key Performance Indicators') {
            financialSheet[cellAddress].s = {
              font: { bold: true, sz: 11 },
              fill: { fgColor: { rgb: "E8F5E8" } }
            };
          }
        }
      }

      XLSX.utils.book_append_sheet(wb, financialSheet, 'Financial Analysis');

      // Set column widths for better readability
      [summarySheet, buildingSheet, tenantSheet, financialSheet].forEach(sheet => {
        if (sheet['!cols']) return;
        sheet['!cols'] = [
          { wch: 25 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 15 },
          { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 20 }, { wch: 25 }
        ];
      });

      // Save the Excel file
      XLSX.writeFile(wb, `rent-collection-report-${currentDate.replace(/\//g, '-')}.xlsx`);
    } catch (error) {
      console.error('Excel Export Error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to generate Excel file. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Filters */}
      <Card className="shadow-sm border-0">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="report-period" className="text-sm font-medium text-gray-700 dark:text-gray-300">Report Period</Label>
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger id="report-period" className="h-10">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map(period => (
                    <SelectItem key={period} value={period}>{period}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="building" className="text-sm font-medium text-gray-700 dark:text-gray-300">Building</Label>
              <Select value={selectedBuilding} onValueChange={setSelectedBuilding}>
                <SelectTrigger id="building" className="h-10">
                  <SelectValue placeholder="Select building" />
                </SelectTrigger>
                <SelectContent>
                  {buildings.map(building => (
                    <SelectItem key={building} value={building}>{building}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {reportPeriod === 'Custom Range' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="start-date" className="text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="h-10"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date" className="text-sm font-medium text-gray-700 dark:text-gray-300">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="h-10"
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex flex-wrap gap-2 pt-2">
            <Button className="bg-blue-600 hover:bg-blue-700 h-10 px-4">
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleExportPDF} className="h-10 px-4">
              <FileDown className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" onClick={handleExportExcel} className="h-10 px-4">
              <ArrowDownToLine className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* First Row - Collection Cards */}
        {/* Rent Collected */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-green-100 dark:bg-green-900/50 flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Rent Collected</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">₹{stats.rentCollected.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Room Rent Collected */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center">
                <Building2 className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Room Rent Collected</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">₹{stats.roomRentCollected.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* GST Collected */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">GST Collected</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">₹{stats.gstCollected.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Deposits */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center">
                <Building2 className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Deposits</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">₹{stats.totalDeposits.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Second Row - Pending Cards */}
        {/* Pending Rent */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-red-100 dark:bg-red-900/50 flex items-center justify-center">
                <TrendingDown className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Rent</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">₹{stats.pendingCashRent.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending Room Rent */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-pink-100 dark:bg-pink-900/50 flex items-center justify-center">
                <Building2 className="h-6 w-6 text-pink-600 dark:text-pink-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Room Rent</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">₹{stats.pendingRoomRent.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending GST */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-orange-100 dark:bg-orange-900/50 flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending GST</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">₹{stats.pendingGST.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Tenants */}
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-xl bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                <Users className="h-6 w-6 text-gray-600 dark:text-gray-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Tenants</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-1">{stats.totalTenants}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
        {/* Collection Summary */}
        <div className="lg:col-span-4">
          <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Monthly Collection Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {collectionSummary.length === 0 ? (
                <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                  <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No collection data found for the selected period</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Building-wise breakdown */}
                  {collectionSummary.map((summary, index) => (
                    <div key={index} className="space-y-3">
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-lg">{summary.building}</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Rent:</span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">₹{summary.rent.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Room Rent:</span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">₹{summary.roomRent.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">GST:</span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">₹{summary.gst.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-base font-semibold pt-1 border-t border-gray-200 dark:border-gray-600">
                          <span className="text-gray-900 dark:text-gray-100">Total:</span>
                          <span className="text-gray-900 dark:text-gray-100">₹{summary.total.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Overall totals */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-6 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Total Rent Collected:</span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">₹{stats.rentCollected.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Total Room Rent Collected:</span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">₹{stats.roomRentCollected.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Total GST Collected:</span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">₹{stats.gstCollected.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-base font-bold pt-2 border-t border-gray-200 dark:border-gray-600 bg-green-50 dark:bg-green-900/20 px-3 py-2 rounded-lg">
                        <span className="text-gray-900 dark:text-gray-100">Grand Total:</span>
                        <span className="text-green-700 dark:text-green-400">₹{(stats.rentCollected + stats.roomRentCollected + stats.gstCollected).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Pending Collections */}
        <div className="lg:col-span-3">
          <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Pending Collections
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {pendingCollections.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <div className="h-12 w-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 opacity-50" />
                  </div>
                  <p className="text-sm font-medium">No pending collections</p>
                  <p className="text-xs">All payments are up to date!</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {pendingCollections.map((pending, index) => (
                    <div key={index} className="p-4 bg-gray-50/50 dark:bg-gray-800/50 rounded-lg border border-gray-200/50 dark:border-gray-700/50 hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                            <Users className="h-5 w-5 text-red-600 dark:text-red-400" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">{pending.tenant.fullName}</div>
                            <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1 mt-0.5">
                              <Building2 className="h-3.5 w-3.5" />
                              {pending.building} - {pending.floor}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-red-600 dark:text-red-400 font-semibold text-sm">₹{pending.cashRent.toLocaleString()} Cash</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{pending.bankRent.toLocaleString()} Bank</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{pending.roomRentAmount.toLocaleString()} Room</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{pending.gstAmount.toLocaleString()} GST</div>
                        </div>
                      </div>
                      {pending.pendingMonths && pending.pendingMonths > 0 && (
                        <div className="mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                          <div className="flex justify-between items-center text-xs">
                            <span className="text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700/50 px-2 py-1 rounded">
                              {pending.pendingMonths} month{pending.pendingMonths > 1 ? 's' : ''} pending
                            </span>
                            <span className="text-gray-600 dark:text-gray-400">
                              ₹{pending.monthlyCashRent.toLocaleString()} Cash + ₹{pending.monthlyBankRent.toLocaleString()} Bank + ₹{pending.monthlyRoomRent.toLocaleString()} Room + ₹{pending.monthlyGST.toLocaleString()} GST
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {pendingCollections.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50 rounded-lg p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending Cash Rent:</span>
                      <span className="font-semibold text-red-600 dark:text-red-400">₹{stats.pendingCashRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending Bank Rent:</span>
                      <span className="font-semibold text-blue-600 dark:text-blue-400">₹{stats.pendingBankRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending Room Rent:</span>
                      <span className="font-semibold text-purple-600 dark:text-purple-400">₹{stats.pendingRoomRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending GST:</span>
                      <span className="font-semibold text-orange-600 dark:text-orange-400">₹{stats.pendingGST.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-base font-bold pt-3 mt-1 border-t border-gray-200 dark:border-gray-700">
                      <span className="text-gray-900 dark:text-gray-100">Total Pending:</span>
                      <span className="text-red-600 dark:text-red-400">₹{(stats.pendingCashRent + stats.pendingBankRent + stats.pendingRoomRent + stats.pendingGST).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
