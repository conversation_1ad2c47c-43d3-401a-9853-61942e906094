import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { ArrowDownToLine, FileDown, Filter, TrendingUp, TrendingDown, DollarSign, Users, Building2, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getAllRentEntries, getAllTenants, type RentEntry, type Tenant } from '@/services/api';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface CollectionSummary {
  building: string;
  rent: number;
  gst: number;
  total: number;
}

interface PendingCollection {
  tenant: Tenant;
  amount: number;
  gstAmount: number;
  building: string;
  floor: string;
  pendingMonths?: number;
  monthlyRent?: number;
  monthlyGST?: number;
}

export const Reports = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [reportPeriod, setReportPeriod] = useState('This Month');
  const [selectedBuilding, setSelectedBuilding] = useState('All Buildings');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  // Summary statistics
  const [stats, setStats] = useState({
    rentCollected: 0,
    gstCollected: 0,
    pendingRent: 0,
    pendingGST: 0,
    totalDeposits: 0,
    totalTenants: 0
  });

  const [collectionSummary, setCollectionSummary] = useState<CollectionSummary[]>([]);
  const [pendingCollections, setPendingCollections] = useState<PendingCollection[]>([]);

  const buildings = ['All Buildings', 'Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const periods = ['This Month', 'This Quarter', 'This Year', 'Custom Range'];

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateSummary();
  }, [rentEntries, tenants, reportPeriod, selectedBuilding, startDate, endDate]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [entriesResponse, tenantsResponse] = await Promise.all([
        getAllRentEntries(),
        getAllTenants()
      ]);

      setRentEntries(entriesResponse.data || []);
      setTenants(tenantsResponse.data || []);
    } catch (error: any) {
      console.error('Error loading report data:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load report data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get date range based on selected period
  const getDateRange = () => {
    const now = new Date();
    let periodStartDate: Date, periodEndDate: Date;

    switch (reportPeriod) {
      case 'This Month':
        periodStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
        periodEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      case 'This Quarter':
        const quarter = Math.floor(now.getMonth() / 3);
        periodStartDate = new Date(now.getFullYear(), quarter * 3, 1);
        periodEndDate = new Date(now.getFullYear(), quarter * 3 + 3, 0);
        break;
      case 'This Year':
        periodStartDate = new Date(now.getFullYear(), 0, 1);
        periodEndDate = new Date(now.getFullYear(), 11, 31);
        break;
      case 'Custom Range':
        if (startDate && endDate) {
          periodStartDate = new Date(startDate);
          periodEndDate = new Date(endDate);
        } else {
          periodStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
          periodEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        }
        break;
      default:
        periodStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
        periodEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    }

    // Set time to start and end of day
    periodStartDate.setHours(0, 0, 0, 0);
    periodEndDate.setHours(23, 59, 59, 999);

    return { startDate: periodStartDate, endDate: periodEndDate };
  };

  // Helper function to get expected rent months for a tenant in the selected period
  const getExpectedRentMonths = (tenant: Tenant, periodStart: Date, periodEnd: Date) => {
    const months = [];
    const rentStart = new Date(tenant.rentStartDate);

    // Set rentStart to the first day of its month
    rentStart.setDate(1);
    rentStart.setHours(0, 0, 0, 0);

    // Use the later of rentStart and periodStart as the starting point
    const current = new Date(Math.max(rentStart.getTime(), periodStart.getTime()));
    current.setDate(1); // Ensure we're at the start of the month

    const end = new Date(periodEnd);
    end.setDate(1); // Set to first of month for comparison

    while (current <= end) {
      months.push(new Date(current));
      current.setMonth(current.getMonth() + 1);
    }

    return months;
  };

  const calculateSummary = () => {
    const { startDate: periodStart, endDate: periodEnd } = getDateRange();

    // Filter entries based on period and building
    const filteredEntries = rentEntries.filter(entry => {
      const entryDate = new Date(entry.rentMonth);
      entryDate.setHours(0, 0, 0, 0); // Normalize time to start of day

      // Period filter
      const isInPeriod = entryDate >= periodStart && entryDate <= periodEnd;

      // Building filter
      const isBuildingMatch = selectedBuilding === 'All Buildings' ||
        entry.building === selectedBuilding;

      return isInPeriod && isBuildingMatch;
    });

    // Calculate collection summary by building
    const summary = filteredEntries.reduce((acc, entry) => {
      const building = entry.building;
      if (!acc[building]) {
        acc[building] = { building, rent: 0, gst: 0, total: 0 };
      }
      if (entry.paymentStatus === 'paid') {
        acc[building].rent += entry.rentDetails.totalRent;
        acc[building].gst += entry.rentDetails.gstAmount;
        acc[building].total += entry.rentDetails.totalPayable;
      }
      return acc;
    }, {} as Record<string, CollectionSummary>);

    // Calculate pending collections based on the selected period
    const pending = tenants
      .filter(tenant => {
        // Filter by building
        if (selectedBuilding !== 'All Buildings' && tenant.building !== selectedBuilding) {
          return false;
        }

        // Only include active tenants
        if (tenant.isActive === false) {
          return false;
        }

        // Check if tenant's rent start date is within or before the period
        const rentStart = new Date(tenant.rentStartDate);
        rentStart.setHours(0, 0, 0, 0);
        return rentStart <= periodEnd;
      })
      .map(tenant => {
        // Get expected rent months for this tenant in the period
        const expectedMonths = getExpectedRentMonths(tenant, periodStart, periodEnd);

        // Get all rent entries for this tenant
        const tenantEntries = rentEntries.filter(entry => {
          let entryTenantId: string;
          if (typeof entry.tenant === 'string') {
            entryTenantId = entry.tenant;
          } else if (entry.tenant && typeof entry.tenant === 'object') {
            entryTenantId = entry.tenant.id || (entry.tenant as any)._id;
          } else {
            return false;
          }

          const tenantId = tenant.id || (tenant as any)._id;
          return entryTenantId === tenantId;
        });

        // Find missing or pending rent entries
        const pendingMonths = expectedMonths.filter(expectedMonth => {
          const monthStr = expectedMonth.toISOString().slice(0, 7); // YYYY-MM format

          // Check if there's a rent entry for this month
          const entryForMonth = tenantEntries.find(entry => {
            const entryMonth = new Date(entry.rentMonth).toISOString().slice(0, 7);
            return entryMonth === monthStr && entry.paymentStatus === 'paid';
          });

          // Consider it pending if no paid entry exists for this month
          return !entryForMonth;
        });

        if (pendingMonths.length === 0) return null;

        // Calculate total pending amount
        const monthlyRent = tenant.rentDetails.totalRent;
        const monthlyGST = tenant.rentDetails.gstAmount;
        const totalPending = monthlyRent * pendingMonths.length;
        const totalPendingGST = monthlyGST * pendingMonths.length;

        return {
          tenant,
          amount: totalPending,
          gstAmount: totalPendingGST,
          building: tenant.building,
          floor: tenant.floor,
          pendingMonths: pendingMonths.length,
          monthlyRent,
          monthlyGST
        };
      })
      .filter((p): p is NonNullable<typeof p> => p !== null && (p.amount > 0 || p.gstAmount > 0));

    // Calculate overall statistics
    const stats = {
      rentCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.totalRent : 0), 0),
      gstCollected: filteredEntries.reduce((sum, entry) =>
        sum + (entry.paymentStatus === 'paid' ? entry.rentDetails.gstAmount : 0), 0),
      pendingRent: pending.reduce((sum, p) => sum + p.amount, 0),
      pendingGST: pending.reduce((sum, p) => sum + p.gstAmount, 0),
      totalDeposits: tenants.reduce((sum, tenant) => sum + tenant.deposit, 0),
      totalTenants: tenants.length
    };

    setCollectionSummary(Object.values(summary));
    setPendingCollections(pending);
    setStats(stats);
  };

  const handleExportPDF = () => {
    // Implement PDF export functionality
    toast({
      title: "Coming Soon",
      description: "PDF export functionality will be available soon",
    });
  };

  const handleExportExcel = () => {
    // Implement Excel export functionality
    toast({
      title: "Coming Soon",
      description: "Excel export functionality will be available soon",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Filters */}
      <Card className="shadow-sm border-0">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="report-period" className="text-sm font-medium text-gray-700 dark:text-gray-300">Report Period</Label>
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger id="report-period" className="h-10">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map(period => (
                    <SelectItem key={period} value={period}>{period}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="building" className="text-sm font-medium text-gray-700 dark:text-gray-300">Building</Label>
              <Select value={selectedBuilding} onValueChange={setSelectedBuilding}>
                <SelectTrigger id="building" className="h-10">
                  <SelectValue placeholder="Select building" />
                </SelectTrigger>
                <SelectContent>
                  {buildings.map(building => (
                    <SelectItem key={building} value={building}>{building}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {reportPeriod === 'Custom Range' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="start-date" className="text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="h-10"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end-date" className="text-sm font-medium text-gray-700 dark:text-gray-300">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="h-10"
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex flex-wrap gap-2 pt-2">
            <Button className="bg-blue-600 hover:bg-blue-700 h-10 px-4">
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleExportPDF} className="h-10 px-4">
              <FileDown className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" onClick={handleExportExcel} className="h-10 px-4">
              <ArrowDownToLine className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Rent Collected</p>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">₹{stats.rentCollected.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">GST Collected</p>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">₹{stats.gstCollected.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-red-100 dark:bg-red-900 flex items-center justify-center">
                <TrendingDown className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Pending Rent</p>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">₹{stats.pendingRent.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-orange-100 dark:bg-orange-900 flex items-center justify-center">
                <Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Pending GST</p>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">₹{stats.pendingGST.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                <Building2 className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Total Deposits</p>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">₹{stats.totalDeposits.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                <Users className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              </div>
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Total Tenants</p>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">{stats.totalTenants}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
        {/* Collection Summary */}
        <div className="lg:col-span-3">
          <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Monthly Collection Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {collectionSummary.length === 0 ? (
                <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                  <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No collection data found for the selected period</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Building-wise breakdown */}
                  {collectionSummary.map((summary, index) => (
                    <div key={index} className="space-y-3">
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-lg">{summary.building}</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Rent:</span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">₹{summary.rent.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">GST:</span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">₹{summary.gst.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-base font-semibold pt-1 border-t border-gray-200 dark:border-gray-600">
                          <span className="text-gray-900 dark:text-gray-100">Total:</span>
                          <span className="text-gray-900 dark:text-gray-100">₹{summary.total.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Overall totals */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-6 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Total Rent Collected:</span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">₹{stats.rentCollected.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Total GST Collected:</span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">₹{stats.gstCollected.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-base font-bold pt-2 border-t border-gray-200 dark:border-gray-600 bg-green-50 dark:bg-green-900/20 px-3 py-2 rounded-lg">
                        <span className="text-gray-900 dark:text-gray-100">Grand Total:</span>
                        <span className="text-green-700 dark:text-green-400">₹{(stats.rentCollected + stats.gstCollected).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Pending Collections */}
        <div className="lg:col-span-4">
          <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Pending Collections
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {pendingCollections.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <div className="h-12 w-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 opacity-50" />
                  </div>
                  <p className="text-sm font-medium">No pending collections</p>
                  <p className="text-xs">All payments are up to date!</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {pendingCollections.map((pending, index) => (
                    <div key={index} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-lg bg-red-100 dark:bg-red-900 flex items-center justify-center">
                            <Users className="h-4 w-4 text-red-600 dark:text-red-400" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">{pending.tenant.fullName}</div>
                            <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {pending.building} - {pending.floor}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-red-600 dark:text-red-400 font-semibold text-sm">₹{pending.amount.toLocaleString()}</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{pending.gstAmount.toLocaleString()} GST</div>
                        </div>
                      </div>
                      {pending.pendingMonths && pending.pendingMonths > 0 && (
                        <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                          <div className="flex justify-between items-center text-xs">
                            <span className="text-gray-600 dark:text-gray-400">
                              {pending.pendingMonths} month{pending.pendingMonths > 1 ? 's' : ''} pending
                            </span>
                            <span className="text-gray-600 dark:text-gray-400">
                              ₹{pending.monthlyRent?.toLocaleString()}/month + ₹{pending.monthlyGST?.toLocaleString()} GST
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {pendingCollections.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending Rent:</span>
                      <span className="font-semibold text-red-600 dark:text-red-400">₹{stats.pendingRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending GST:</span>
                      <span className="font-semibold text-orange-600 dark:text-orange-400">₹{stats.pendingGST.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-base font-bold pt-2 border-t border-gray-200 dark:border-gray-600">
                      <span className="text-gray-900 dark:text-gray-100">Total Pending:</span>
                      <span className="text-red-600 dark:text-red-400">₹{(stats.pendingRent + stats.pendingGST).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
