const mongoose = require('mongoose');

const rentIncrementSchema = new mongoose.Schema({
    tenant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Tenant',
        required: [true, 'Please provide tenant ID']
    },
    oldRent: {
        type: Number,
        required: [true, 'Please provide old rent amount']
    },
    newRent: {
        type: Number,
        required: [true, 'Please provide new rent amount']
    },
    incrementPercentage: {
        type: Number,
        required: [true, 'Please provide increment percentage']
    },
    incrementAmount: {
        type: Number,
        required: true
    },
    effectiveDate: {
        type: Date,
        required: [true, 'Please provide effective date']
    },
    appliedDate: {
        type: Date,
        default: Date.now
    },
    reason: {
        type: String,
        default: 'Annual increment'
    },
    status: {
        type: String,
        enum: ['pending', 'applied', 'cancelled'],
        default: 'applied'
    },
    oldRentDetails: {
        cashRent: Number,
        bankRent: Number,
        roomRent: Number,
        gstAmount: Number,
        totalRent: Number,
        totalPayable: Number
    },
    newRentDetails: {
        cashRent: Number,
        bankRent: Number,
        roomRent: Number,
        gstAmount: Number,
        totalRent: Number,
        totalPayable: Number
    },
    notes: {
        type: String
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Calculate increment amount before saving
rentIncrementSchema.pre('save', function (next) {
    this.incrementAmount = this.newRent - this.oldRent;
    this.updatedAt = Date.now();
    next();
});

// Index for efficient queries
rentIncrementSchema.index({ tenant: 1, effectiveDate: -1 });
rentIncrementSchema.index({ effectiveDate: 1 });
rentIncrementSchema.index({ status: 1 });

module.exports = mongoose.model('RentIncrement', rentIncrementSchema);
