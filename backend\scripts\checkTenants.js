const mongoose = require('mongoose');
const Tenant = require('../models/Tenant');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/rent-guardian')
    .then(() => console.log('Connected to MongoDB'))
    .catch((err) => console.error('MongoDB connection error:', err));

async function checkTenants() {
    try {
        const tenants = await Tenant.find({}).limit(10);
        
        console.log(`Found ${tenants.length} tenants:`);
        
        tenants.forEach(tenant => {
            console.log(`\n--- ${tenant.fullName} ---`);
            console.log(`ID: ${tenant._id}`);
            console.log(`Next Increment Date: ${tenant.nextIncrementDate}`);
            console.log(`Rent Details:`, tenant.rentDetails);
            console.log(`Is Active: ${tenant.isActive}`);
        });

        // Check for upcoming increments (next 90 days)
        const today = new Date();
        const futureDate = new Date();
        futureDate.setDate(today.getDate() + 90);

        const upcomingIncrements = await Tenant.find({
            isActive: true,
            nextIncrementDate: { 
                $exists: true, 
                $ne: null,
                $gte: today,
                $lte: futureDate
            }
        });

        console.log(`\n\nUpcoming increments (next 90 days): ${upcomingIncrements.length}`);
        upcomingIncrements.forEach(tenant => {
            const incrementDate = new Date(tenant.nextIncrementDate);
            const daysRemaining = Math.ceil((incrementDate - today) / (1000 * 60 * 60 * 24));
            console.log(`${tenant.fullName}: ${incrementDate.toISOString().split('T')[0]} (${daysRemaining} days)`);
        });

        process.exit(0);
    } catch (error) {
        console.error('Error checking tenants:', error);
        process.exit(1);
    }
}

checkTenants();
