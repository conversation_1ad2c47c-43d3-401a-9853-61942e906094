import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Card } from './ui/card';
import { ArrowLeft, Edit, FileText, Download, Upload, Eye, Trash2 } from 'lucide-react';
import { getTenant, getTenantRentEntries, getTenantDocuments, downloadDocument, uploadDocument, deleteTenant, type Tenant, type RentEntry, type Document } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from './ui/alert-dialog';

interface TenantDetailsProps {
  tenantId: string;
  onBack: () => void;
  onEdit: () => void;
}

const documentTypes = [
  'Rental Agreement',
  'Aadhar Card Copy',
  'Police Verification',
  'Company ID',
  'Other'
];

const getStatusText = (status: string) => {
  switch (status) {
    case 'paid':
      return 'Paid';
    case 'unpaid':
      return 'Unpaid';
    case 'increment-due':
      return 'Increment Due';
    case 'active':
      return 'Active';
    case 'inactive':
      return 'Inactive';
    default:
      return status ? status.charAt(0).toUpperCase() + status.slice(1) : '';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'unpaid':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'increment-due':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'active':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'inactive':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    default:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
  }
};

export const TenantDetails: React.FC<TenantDetailsProps> = ({ tenantId, onBack, onEdit }) => {
  const { toast } = useToast();
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState('');
  const [customLabel, setCustomLabel] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    loadData();
  }, [tenantId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [tenantResponse, rentResponse, documentsResponse] = await Promise.all([
        getTenant(tenantId),
        getTenantRentEntries(tenantId),
        getTenantDocuments(tenantId)
      ]);
      setTenant(tenantResponse.data);
      setRentEntries(rentResponse.data);
      setDocuments(documentsResponse.data);
    } catch (error: any) {
      console.error('Error loading tenant details:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load tenant details",
        variant: "destructive",
      });
      onBack();
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !documentType) {
      toast({
        title: "Error",
        description: "Please select a file and document type",
        variant: "destructive",
      });
      return;
    }

    if (documentType === 'Other' && !customLabel) {
      toast({
        title: "Error",
        description: "Please enter a custom label for the document",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(true);
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('tenantId', tenantId);
      formData.append('documentType', documentType === 'Other' ? customLabel : documentType);

      await uploadDocument(formData);
      toast({
        title: "Success",
        description: "Document uploaded successfully",
      });

      // Reset form and refresh documents
      setSelectedFile(null);
      setDocumentType('');
      setCustomLabel('');
      const documentsResponse = await getTenantDocuments(tenantId);
      setDocuments(documentsResponse.data);
    } catch (error: any) {
      console.error('Error uploading document:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to upload document",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = async (documentId: string) => {
    try {
      const blob = await downloadDocument(documentId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'document';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error: any) {
      console.error('Error downloading document:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to download document",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTenant = async () => {
    if (!tenant?.id) {
      toast({
        title: "Error",
        description: "Cannot delete tenant: No tenant ID found",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsDeleting(true);
      await deleteTenant(tenant.id);

      toast({
        title: "Success",
        description: `Tenant ${tenant.fullName} has been deleted successfully`,
      });

      // Navigate back to tenant list
      onBack();
    } catch (error: any) {
      console.error('Error deleting tenant:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete tenant",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-500 dark:text-gray-400">Loading tenant details...</p>
      </div>
    );
  }

  if (!tenant) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <p className="text-gray-500 dark:text-gray-400 mb-4">Tenant not found</p>
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tenants
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Tenants
          </Button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{tenant.fullName}</h1>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={onEdit} className="bg-blue-600 hover:bg-blue-700">
            <Edit className="h-4 w-4 mr-2" />
            Edit Tenant
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:border-red-400 dark:hover:bg-red-900/20"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Tenant
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Tenant</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete <strong>{tenant.fullName}</strong>?
                  This action cannot be undone and will permanently remove all tenant data including rent history and documents.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteTenant}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Personal Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Personal Information</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</label>
              <p className="text-gray-900 dark:text-white">{tenant.fullName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Firm/Company</label>
              <p className="text-gray-900 dark:text-white">{tenant.firmName || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
              <p className="text-gray-900 dark:text-white">{tenant.phone}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
              <p className="text-gray-900 dark:text-white">{tenant.email || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Address</label>
              <p className="text-gray-900 dark:text-white">{tenant.address}</p>
            </div>
          </div>
        </Card>

        {/* Property & Rent Details */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Property & Rent Details</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Property</label>
              <p className="text-gray-900 dark:text-white">{tenant.building} - {tenant.floor}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Cash Rent</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.cashRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Bank Rent</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.bankRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Room Rent</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.roomRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">GST (18%)</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.rentDetails.gstAmount.toLocaleString()}</p>
            </div>
            <div className="border-t pt-3">
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Rent</label>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">₹{tenant.rentDetails.totalRent.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Payable (Inc. GST)</label>
              <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">₹{tenant.rentDetails.totalPayable.toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Deposit</label>
              <p className="text-gray-900 dark:text-white">₹{tenant.deposit.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        {/* Additional Info */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Additional Information</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Rent Start Date</label>
              <p className="text-gray-900 dark:text-white">{format(new Date(tenant.rentStartDate), 'PPP')}</p>
            </div>
            {tenant.nextIncrementDate && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Next Increment Date</label>
                <p className="text-gray-900 dark:text-white">{format(new Date(tenant.nextIncrementDate), 'PPP')}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
              <div className="flex flex-col gap-1 mt-1">
                {tenant.status && (
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(tenant.status)}`}>
                    {getStatusText(tenant.status)}
                  </span>
                )}
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(tenant.isActive ? 'active' : 'inactive')}`}>
                  {tenant.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
            {tenant.notes && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</label>
                <p className="text-gray-900 dark:text-white text-sm whitespace-pre-wrap">{tenant.notes}</p>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Rent Collection History */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Rent Collection History</h3>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export History
          </Button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Cash Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Bank Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Room Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">GST (18%)</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Payable</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Method</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {rentEntries.map((entry) => (
                <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {format(new Date(entry.rentMonth), 'MMMM yyyy')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{entry.rentDetails.cashRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{entry.rentDetails.bankRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{entry.rentDetails.roomRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{entry.rentDetails.gstAmount.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    ₹{entry.rentDetails.totalRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 dark:text-blue-400">
                    ₹{entry.rentDetails.totalPayable.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(entry.paymentStatus)}`}>
                      {getStatusText(entry.paymentStatus)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {entry.rentDetails.bankRent > 0 ? 'Bank Transfer' : 'Cash'}
                  </td>
                </tr>
              ))}
              {rentEntries.length === 0 && (
                <tr key="no-entries">
                  <td colSpan={9} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    No rent entries found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Documents */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Documents</h3>
          <div className="flex items-center space-x-4">
            <select
              value={documentType}
              onChange={(e) => {
                setDocumentType(e.target.value);
                if (e.target.value !== 'Other') {
                  setCustomLabel('');
                }
              }}
              className="px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="">Select Document Type</option>
              {documentTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
            {documentType === 'Other' && (
              <input
                type="text"
                value={customLabel}
                onChange={(e) => setCustomLabel(e.target.value)}
                placeholder="Enter document label"
                className="px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            )}
            <input
              type="file"
              onChange={handleFileChange}
              className="hidden"
              id="document-upload"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            />
            <label htmlFor="document-upload">
              <Button variant="outline" size="sm" asChild>
                <span>
                  <Upload className="h-4 w-4 mr-2" />
                  Choose File
                </span>
              </Button>
            </label>
            <Button
              size="sm"
              onClick={handleUpload}
              disabled={!selectedFile || !documentType || (documentType === 'Other' && !customLabel) || isUploading}
            >
              {isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Upload Document
                </>
              )}
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {documents.map((doc) => (
            <div key={doc.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">{doc.documentType}</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Uploaded: {format(new Date(doc.uploadDate), 'PP')}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDownload(doc.id!)}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
          {documents.length === 0 && (
            <div className="col-span-3 border border-gray-200 dark:border-gray-700 rounded-lg p-4 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <p>No documents uploaded yet</p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};
