
import React from 'react';
import { Users, DollarSign, Shield, Calendar, AlertTriangle } from 'lucide-react';

const statsCards = [
  {
    title: 'Total Tenants',
    value: '47',
    icon: Users,
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
  },
  {
    title: 'Rent Collected (This Month)',
    value: '₹2,45,000',
    icon: DollarSign,
    color: 'bg-green-500',
    textColor: 'text-green-700',
    bgColor: 'bg-green-50',
  },
  {
    title: 'Total Deposits Held',
    value: '₹12,50,000',
    icon: Shield,
    color: 'bg-purple-500',
    textColor: 'text-purple-700',
    bgColor: 'bg-purple-50',
  },
  {
    title: 'Upcoming Increments (30 Days)',
    value: '12',
    icon: Calendar,
    color: 'bg-orange-500',
    textColor: 'text-orange-700',
    bgColor: 'bg-orange-50',
  },
  {
    title: 'Overdue Rent',
    value: '8',
    icon: AlertTriangle,
    color: 'bg-red-500',
    textColor: 'text-red-700',
    bgColor: 'bg-red-50',
  },
];

export const DashboardOverview: React.FC = () => {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Overview</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {statsCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg ${card.bgColor}`}>
                  <Icon className={`h-6 w-6 ${card.textColor}`} />
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-600 mb-1">{card.title}</h4>
                <p className="text-2xl font-bold text-gray-900">{card.value}</p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
