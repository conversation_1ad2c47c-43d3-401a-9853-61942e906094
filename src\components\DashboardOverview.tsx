import React, { useState, useEffect } from 'react';
import { Users, DollarSign, Shield, Calendar, AlertTriangle, Building2 } from 'lucide-react';
import { getAllTenants, getAllRentEntries, getUpcomingRentIncrements } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { Card } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface PendingRent {
  tenant: any;
  building: string;
  floor: string;
  cashRent: number;
  bankRent: number;
  roomRent: number;
  gstAmount: number;
  totalAmount: number;
  pendingMonths: number;
  monthlyCashRent: number;
  monthlyBankRent: number;
  monthlyRoomRent: number;
  monthlyGST: number;
  monthlyTotal: number;
  pendingMonthsList: { month: number; year: number }[];
}

export const DashboardOverview: React.FC = () => {
  const { toast } = useToast();
  const [stats, setStats] = useState({
    totalTenants: 0,
    rentCollectedThisMonth: 0,
    totalDeposits: 0,
    upcomingIncrements: 0,
    overdueRent: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [pendingRents, setPendingRents] = useState<PendingRent[]>([]);

  useEffect(() => {
    fetchDashboardStats();
  }, [selectedYear]);

  const fetchDashboardStats = async () => {
    try {
      setIsLoading(true);

      // Fetch all required data in parallel
      const [tenantsResponse, rentEntriesResponse, incrementsResponse] = await Promise.all([
        getAllTenants(),
        getAllRentEntries(),
        getUpcomingRentIncrements(30) // Get increments due in next 30 days
      ]);

      const tenants = tenantsResponse.data || [];
      const rentEntries = rentEntriesResponse.data || [];
      const increments = incrementsResponse.data || [];

      // Calculate total tenants
      const totalTenants = tenants.length;

      // Calculate total deposits
      const totalDeposits = tenants.reduce((sum, tenant) => sum + (tenant.deposit || 0), 0);

      // Get current month and year
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();

      // Calculate rent collected this month
      const rentCollectedThisMonth = rentEntries
        .filter(entry => {
          const entryDate = new Date(entry.rentMonth);
          return entryDate.getMonth() === currentMonth &&
            entryDate.getFullYear() === currentYear &&
            entry.paymentStatus === 'paid';
        })
        .reduce((sum, entry) => sum + entry.rentDetails.totalPayable, 0);

      // Count upcoming increments
      const upcomingIncrements = increments.length;

      // Count overdue rent entries
      const overdueRent = rentEntries.filter(entry =>
        entry.paymentStatus === 'pending' || entry.paymentStatus === 'partial'
      ).length;

      setStats({
        totalTenants,
        rentCollectedThisMonth,
        totalDeposits,
        upcomingIncrements,
        overdueRent
      });

      // Calculate pending rents for selected year
      const selectedYearNum = parseInt(selectedYear);
      const pendingRentsList = calculatePendingRents(tenants, rentEntries, selectedYearNum);
      setPendingRents(pendingRentsList);

    } catch (error: any) {
      console.error('Error fetching dashboard stats:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch dashboard statistics",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculatePendingRents = (tenants: any[], rentEntries: any[], selectedYear: number) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Helper function to normalize date to YYYY-MM format for comparison
    const normalizeDate = (date: Date) => {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    };

    return tenants
      .filter(tenant => tenant.isActive)
      .map(tenant => {
        // Convert tenant start date to Date object
        const tenantStartDate = new Date(tenant.rentStartDate);
        tenantStartDate.setDate(1); // Set to first of month for comparison
        tenantStartDate.setHours(0, 0, 0, 0);

        // Calculate the range of months to check
        const monthsToCheck = [];
        const startDate = new Date(tenantStartDate);
        const endDate = new Date(currentYear, currentMonth, 1);

        // Generate all months from start date to current date
        let currentCheck = new Date(startDate);
        while (currentCheck <= endDate) {
          monthsToCheck.push(new Date(currentCheck));
          currentCheck.setMonth(currentCheck.getMonth() + 1);
        }

        // Get paid entries for this tenant
        const paidEntries = rentEntries.filter(entry => {
          let entryTenantId: string;
          if (typeof entry.tenant === 'string') {
            entryTenantId = entry.tenant;
          } else if (entry.tenant && typeof entry.tenant === 'object') {
            entryTenantId = entry.tenant.id || (entry.tenant as any)._id;
          } else {
            return false;
          }

          const tenantId = tenant.id || (tenant as any)._id;
          return entryTenantId === tenantId && entry.paymentStatus === 'paid';
        });

        // Find pending months by comparing with paid entries
        const pendingMonths = monthsToCheck.filter(monthToCheck => {
          const monthNormalized = normalizeDate(monthToCheck);
          const isPaid = paidEntries.some(entry => {
            const entryDate = new Date(entry.rentMonth);
            return normalizeDate(entryDate) === monthNormalized;
          });
          return !isPaid;
        });

        // Filter pending months for selected year
        const selectedYearPendingMonths = pendingMonths.filter(date =>
          date.getFullYear() === selectedYear
        );

        if (selectedYearPendingMonths.length === 0) return null;

        // Calculate monthly amounts
        const monthlyCashRent = tenant.rentDetails.cashRent;
        const monthlyBankRent = tenant.rentDetails.bankRent;
        const monthlyRoomRent = tenant.rentDetails.roomRent || 0;
        const monthlyGST = tenant.rentDetails.gstAmount;
        const monthlyTotal = tenant.rentDetails.totalPayable;

        // Calculate total pending amounts for selected year
        const numPendingMonths = selectedYearPendingMonths.length;
        const totalPendingCashRent = monthlyCashRent * numPendingMonths;
        const totalPendingBankRent = monthlyBankRent * numPendingMonths;
        const totalPendingRoomRent = monthlyRoomRent * numPendingMonths;
        const totalPendingGST = monthlyGST * numPendingMonths;

        // Sort pending months for selected year (most recent first)
        const sortedPendingMonths = [...selectedYearPendingMonths].sort((a, b) => b.getTime() - a.getTime());

        return {
          tenant,
          building: tenant.building,
          floor: tenant.floor,
          cashRent: totalPendingCashRent,
          bankRent: totalPendingBankRent,
          roomRent: totalPendingRoomRent,
          gstAmount: totalPendingGST,
          totalAmount: monthlyTotal * numPendingMonths,
          pendingMonths: numPendingMonths,
          monthlyCashRent,
          monthlyBankRent,
          monthlyRoomRent,
          monthlyGST,
          monthlyTotal,
          pendingMonthsList: sortedPendingMonths.map(date => ({
            month: date.getMonth(),
            year: date.getFullYear()
          }))
        };
      })
      .filter((item): item is PendingRent => item !== null)
      .sort((a, b) => {
        // Sort by number of pending months (descending)
        if (b.pendingMonths !== a.pendingMonths) {
          return b.pendingMonths - a.pendingMonths;
        }
        // If same number of pending months, sort by most recent pending month
        const aLatestMonth = a.pendingMonthsList[0];
        const bLatestMonth = b.pendingMonthsList[0];
        return (bLatestMonth.year * 12 + bLatestMonth.month) - (aLatestMonth.year * 12 + aLatestMonth.month);
      });
  };

  const statsCards = [
    {
      title: 'Total Tenants',
      value: stats.totalTenants.toString(),
      icon: Users,
      iconBg: 'bg-blue-100 dark:bg-blue-900/30',
      iconColor: 'text-blue-600 dark:text-blue-400'
    },
    {
      title: 'Rent Collected (This Month)',
      value: `₹${stats.rentCollectedThisMonth.toLocaleString()}`,
      icon: DollarSign,
      iconBg: 'bg-green-100 dark:bg-green-900/30',
      iconColor: 'text-green-600 dark:text-green-400'
    },
    {
      title: 'Total Deposits Held',
      value: `₹${stats.totalDeposits.toLocaleString()}`,
      icon: Shield,
      iconBg: 'bg-purple-100 dark:bg-purple-900/30',
      iconColor: 'text-purple-600 dark:text-purple-400'
    },
    {
      title: 'Pending Rents',
      value: `₹${stats.totalPendingAmount.toLocaleString()}`,
      icon: CreditCard,
      iconBg: 'bg-yellow-100 dark:bg-yellow-900/30',
      iconColor: 'text-yellow-600 dark:text-yellow-400'
    },
    {
      title: 'Upcoming Increments (30 Days)',
      value: stats.upcomingIncrements.toString(),
      icon: Calendar,
      iconBg: 'bg-orange-100 dark:bg-orange-900/30',
      iconColor: 'text-orange-600 dark:text-orange-400'
    },
    {
      title: 'Overdue Rent',
      value: stats.overdueRent.toString(),
      icon: AlertTriangle,
      iconBg: 'bg-red-100 dark:bg-red-900/30',
      iconColor: 'text-red-600 dark:text-red-400'
    },
  ];

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-500 dark:text-gray-400">Loading dashboard...</p>
      </div>
    );
  }

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => (currentYear - i).toString());

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {statsCards.map((card, index) => (
          <Card
            key={index}
            className="bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-200 dark:border-gray-700"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`h-12 w-12 rounded-lg ${card.iconBg} flex items-center justify-center flex-shrink-0`}>
                  <card.icon className={`h-6 w-6 ${card.iconColor}`} />
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 leading-tight">
                  {card.title}
                </h3>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 leading-none">
                  {card.value}
                </p>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Pending Rent List */}
      <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Pending Rent List</h2>
            <Select value={selectedYear} onValueChange={setSelectedYear}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
              <SelectContent>
                {years.map(year => (
                  <SelectItem key={year} value={year}>{year}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            {pendingRents.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No pending rents found for {selectedYear}</p>
              </div>
            ) : (
              <>
                {pendingRents.map((pending, index) => (
                  <div key={index} className="p-4 bg-gray-50/50 dark:bg-gray-800/50 rounded-lg border border-gray-200/50 dark:border-gray-700/50 hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                          <Users className="h-5 w-5 text-red-600 dark:text-red-400" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">{pending.tenant.fullName}</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1 mt-0.5">
                            <Building2 className="h-3.5 w-3.5" />
                            {pending.building} - {pending.floor}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-red-600 dark:text-red-400 font-semibold text-sm">₹{pending.cashRent.toLocaleString()} Cash</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{pending.bankRent.toLocaleString()} Bank</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{pending.roomRent.toLocaleString()} Room</div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">+ ₹{pending.gstAmount.toLocaleString()} GST</div>
                      </div>
                    </div>
                    <div className="mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                      <div className="flex flex-col gap-2">
                        <div className="flex justify-between items-center text-xs">
                          <span className="text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700/50 px-2 py-1 rounded">
                            {pending.pendingMonths} month{pending.pendingMonths > 1 ? 's' : ''} pending
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">
                            Monthly: ₹{pending.monthlyCashRent.toLocaleString()} Cash + ₹{pending.monthlyBankRent.toLocaleString()} Bank + ₹{pending.monthlyRoomRent.toLocaleString()} Room + ₹{pending.monthlyGST.toLocaleString()} GST
                          </span>
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          Pending for: {pending.pendingMonthsList.map(date => {
                            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                            return monthNames[date.month];
                          }).join(', ')}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending Cash Rent:</span>
                      <span className="font-semibold text-red-600 dark:text-red-400">
                        ₹{pendingRents.reduce((sum, p) => sum + p.cashRent, 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending Bank Rent:</span>
                      <span className="font-semibold text-blue-600 dark:text-blue-400">
                        ₹{pendingRents.reduce((sum, p) => sum + p.bankRent, 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending Room Rent:</span>
                      <span className="font-semibold text-purple-600 dark:text-purple-400">
                        ₹{pendingRents.reduce((sum, p) => sum + p.roomRent, 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Pending GST:</span>
                      <span className="font-semibold text-orange-600 dark:text-orange-400">
                        ₹{pendingRents.reduce((sum, p) => sum + p.gstAmount, 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-base font-bold pt-3 mt-1 border-t border-gray-200 dark:border-gray-700">
                      <span className="text-gray-900 dark:text-gray-100">Total Pending Amount:</span>
                      <span className="text-red-600 dark:text-red-400">
                        ₹{pendingRents.reduce((sum, p) => sum + p.totalAmount, 0).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};
