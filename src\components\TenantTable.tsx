import React, { useState, useEffect } from 'react';
import { Search, Filter, Download, Eye, Edit, Plus } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { getAllTenants, Tenant } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface TenantTableProps {
  showFilters?: boolean;
  onAddTenant?: () => void;
  onViewTenant?: (tenantId: string) => void;
  onEditTenant?: (tenantId: string) => void;
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'paid':
      return 'Paid';
    case 'unpaid':
      return 'Unpaid';
    case 'increment-due':
      return 'Increment Due';
    default:
      return status;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'unpaid':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    case 'increment-due':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
  }
};

const getActiveStatusColor = (isActive: boolean) => {
  return isActive
    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
};

const TenantTable: React.FC<TenantTableProps> = ({
  showFilters = false,
  onAddTenant,
  onViewTenant,
  onEditTenant
}) => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBuilding, setSelectedBuilding] = useState('all');
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTenants();
  }, []);

  const loadTenants = async () => {
    try {
      setIsLoading(true);
      const response = await getAllTenants();
      setTenants(response.data);
    } catch (error: any) {
      console.error('Error loading tenants:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load tenants. Please check if the server is running.",
        variant: "destructive",
      });
      setTenants([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  const buildings = [...new Set(tenants.map(tenant => tenant.building))];

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.building.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesBuilding = selectedBuilding === 'all' || tenant.building === selectedBuilding;
    return matchesSearch && matchesBuilding;
  });

  // Add retry button if no tenants are loaded
  if (!isLoading && tenants.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div className="flex flex-col items-center justify-center text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            No tenants found. This might be because:
          </p>
          <ul className="list-disc text-left text-gray-500 dark:text-gray-400 mb-4">
            <li>The backend server is not running</li>
            <li>The server is not running on port 5050</li>
            <li>There are no tenants in the database</li>
          </ul>
          <div className="space-y-4">
            <Button
              onClick={loadTenants}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Retry Loading
            </Button>
            {onAddTenant && (
              <div className="mt-2">
                <Button onClick={onAddTenant} variant="outline">
                  Add New Tenant
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Tenant Management</h3>
          <div className="flex gap-3">
            {onAddTenant && (
              <Button onClick={onAddTenant} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Add New Tenant
              </Button>
            )}
            <Button className="bg-green-600 hover:bg-green-700">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {showFilters && (
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search tenants or buildings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedBuilding}
              onChange={(e) => setSelectedBuilding(e.target.value)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All Buildings</option>
              {buildings.map((building) => (
                <option key={building} value={building}>{building}</option>
              ))}
            </select>
          </div>
        )}
      </div>

      <div className="overflow-x-auto">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Tenant</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Building & Floor</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Cash Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Bank Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Room Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">GST (18%)</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Payable</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Deposit</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {filteredTenants.map((tenant, index) => (
                <tr
                  key={tenant.id || `${tenant.fullName}-${tenant.building}-${index}`}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900 dark:text-white">{tenant.fullName}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{tenant.firmName}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">{tenant.building}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{tenant.floor}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{tenant.rentDetails.cashRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{tenant.rentDetails.bankRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{tenant.rentDetails.roomRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{tenant.rentDetails.gstAmount.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    ₹{tenant.rentDetails.totalRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 dark:text-blue-400">
                    ₹{tenant.rentDetails.totalPayable.toLocaleString()}
                    <div className="text-xs text-gray-500 dark:text-gray-400">(Inc. GST)</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    ₹{tenant.deposit.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col gap-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(tenant.status || '')}`}>
                        {getStatusText(tenant.status || '')}
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActiveStatusColor(tenant.isActive ?? true)}`}>
                        {tenant.isActive ?? true ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      {tenant.id ? (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewTenant?.(tenant.id)}
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditTenant?.(tenant.id)}
                            title="Edit Tenant"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <span className="text-sm text-red-500">Error: No tenant ID</span>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default TenantTable;
