const mongoose = require('mongoose');

const tenantSchema = new mongoose.Schema({
    fullName: {
        type: String,
        required: [true, 'Please provide tenant name'],
        trim: true
    },
    firmName: {
        type: String,
        trim: true
    },
    phone: {
        type: String,
        required: [true, 'Please provide phone number'],
        trim: true
    },
    email: {
        type: String,
        match: [/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email'],
        trim: true
    },
    address: {
        type: String,
        required: [true, 'Please provide address']
    },
    building: {
        type: String,
        required: [true, 'Please provide building name']
    },
    floor: {
        type: String,
        required: [true, 'Please provide floor']
    },
    rentDetails: {
        cashRent: {
            type: Number,
            required: [true, 'Please provide cash rent amount']
        },
        bankRent: {
            type: Number,
            default: 0
        },
        roomRent: {
            type: Number,
            default: 0
        },
        gstAmount: {
            type: Number,
            default: 0
        },
        totalRent: {
            type: Number,
            required: true
        },
        totalPayable: {
            type: Number,
            required: true
        }
    },
    deposit: {
        type: Number,
        required: [true, 'Please provide deposit amount']
    },
    rentStartDate: {
        type: Date,
        required: [true, 'Please provide rent start date']
    },
    nextIncrementDate: {
        type: Date
    },
    notes: {
        type: String
    },
    isActive: {
        type: Boolean,
        default: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Update timestamps on save
tenantSchema.pre('save', function (next) {
    this.updatedAt = Date.now();
    next();
});

module.exports = mongoose.model('Tenant', tenantSchema); 