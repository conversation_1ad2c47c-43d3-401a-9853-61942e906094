const axios = require('axios');

async function testApplyIncrement() {
    try {
        // First, login to get a token
        const loginResponse = await axios.post('http://localhost:5050/api/auth/login', {
            email: '<EMAIL>',
            password: 'admin123'
        });

        const token = loginResponse.data.token;
        console.log('Login successful, token received');

        // Apply increment for <PERSON><PERSON>
        const incrementData = {
            tenantId: '68380343e0901f0e1ffa205f', // <PERSON><PERSON>'s ID
            incrementPercentage: 5,
            effectiveDate: '2025-05-31',
            reason: 'Annual increment',
            notes: 'Test increment application'
        };

        const response = await axios.post('http://localhost:5050/api/rent-increments/apply', incrementData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('Increment applied successfully:', response.data);
    } catch (error) {
        console.error('Error applying increment:', error.response?.data || error.message);
    }
}

testApplyIncrement();
