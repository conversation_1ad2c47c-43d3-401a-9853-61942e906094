import axios from 'axios';

const API_URL = 'http://localhost:5050/api';

// Add connection status check
const checkConnection = async () => {
    try {
        const token = localStorage.getItem('token');
        const response = await axios.get(`${API_URL}/tenants`, {
            headers: {
                Authorization: token ? `Bearer ${token}` : '',
            },
            timeout: 5000
        });
        return true;
    } catch (error: any) {
        if (error.response) {
            // If we get any response, even an error, the server is running
            return error.response.status !== 404;
        }
        console.error('API connection error:', error);
        return false;
    }
};

const api = axios.create({
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
    // Add timeout
    timeout: 5000,
});

// Add response interceptor for better error handling
api.interceptors.response.use(
    (response) => response,
    async (error) => {
        if (error.code === 'ECONNABORTED') {
            console.error('Request timeout');
            throw new Error('Request timeout - please check your connection');
        }

        if (!error.response) {
            console.error('Network error:', error);
            throw new Error('Cannot connect to server - please make sure the backend server is running on port 5050');
        }

        if (error.response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.reload();
            throw new Error('Session expired - please log in again');
        }

        if (error.response.status === 404) {
            throw new Error('API endpoint not found - please check your server configuration');
        }

        return Promise.reject(error);
    }
);

// Add request interceptor to include the token in requests
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

export const login = async (email: string, password: string) => {
    try {
        const response = await api.post('/auth/login', { email, password });
        if (response.data.token) {
            localStorage.setItem('token', response.data.token);
            localStorage.setItem('user', JSON.stringify(response.data.user));
        }
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const register = async (name: string, email: string, password: string) => {
    try {
        const response = await api.post('/auth/register', { name, email, password });
        if (response.data.token) {
            localStorage.setItem('token', response.data.token);
            localStorage.setItem('user', JSON.stringify(response.data.user));
        }
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getCurrentUser = async () => {
    try {
        const response = await api.get('/auth/me');
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
};

export const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
        const response = await api.post('/auth/change-password', {
            currentPassword,
            newPassword
        });
        return response.data;
    } catch (error) {
        throw error;
    }
};

// Tenant interfaces
export interface RentDetails {
    cashRent: number;
    bankRent: number;
    roomRent: number;
    gst: number;
    gstAmount?: number;
    totalRent?: number;
    totalPayable?: number;
}

export interface Tenant {
    id?: string;
    fullName: string;
    firmName: string;
    phone: string;
    email: string;
    address: string;
    building: string;
    floor: string;
    rentDetails: {
        cashRent: number;
        bankRent: number;
        roomRent: number;
        gst: number;
        gstAmount: number;
        totalRent: number;
        totalPayable: number;
    };
    deposit: number;
    rentStartDate: string;
    nextIncrementDate: string;
    notes: string;
    status?: 'paid' | 'unpaid' | 'increment-due';
    isActive?: boolean;
}

// Tenant API functions
export const createTenant = async (tenantData: Tenant) => {
    try {
        const response = await api.post('/tenants', tenantData);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getAllTenants = async () => {
    try {
        const response = await api.get('/tenants');
        if (response.data.success && Array.isArray(response.data.data)) {
            // Transform the data to ensure id field is present
            const transformedData = response.data.data.map((tenant: any) => ({
                ...tenant,
                id: tenant._id || tenant.id
            }));
            return { data: transformedData };
        } else {
            console.error('Invalid response format:', response.data);
            throw new Error('Invalid response format from server');
        }
    } catch (error: any) {
        console.error('Error fetching tenants:', error);
        if (error.response?.data?.message) {
            throw new Error(error.response.data.message);
        }
        throw error;
    }
};

export const getTenant = async (id: string) => {
    try {
        if (!id) {
            throw new Error('Tenant ID is required');
        }
        const response = await api.get(`/tenants/${id}`);
        if (response.data.success && response.data.data) {
            return { data: response.data.data };
        } else {
            throw new Error('Invalid response format from server');
        }
    } catch (error: any) {
        console.error('Error fetching tenant:', error);
        throw error;
    }
};

export const updateTenant = async (id: string, tenantData: Tenant) => {
    try {
        const response = await api.put(`/tenants/${id}`, tenantData);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const deleteTenant = async (id: string) => {
    try {
        const response = await api.delete(`/tenants/${id}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

// Document interfaces
export interface Document {
    id?: string;
    tenant: string | { fullName: string; id: string };
    documentType: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    expiryDate?: string;
    uploadDate: string;
}

// Document API functions
export const uploadDocument = async (formData: FormData) => {
    try {
        const response = await api.post('/documents/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        return response.data;
    } catch (error: any) {
        if (error.response?.data?.message) {
            throw new Error(error.response.data.message);
        }
        throw error;
    }
};

export const getAllDocuments = async () => {
    try {
        const response = await api.get('/documents');
        if (response.data.success && Array.isArray(response.data.data)) {
            return { data: response.data.data };
        } else {
            throw new Error('Invalid response format from server');
        }
    } catch (error: any) {
        if (error.response?.data?.message) {
            throw new Error(error.response.data.message);
        }
        throw error;
    }
};

export const getTenantDocuments = async (tenantId: string) => {
    try {
        const response = await api.get(`/documents/tenant/${tenantId}`);
        if (response.data.success && Array.isArray(response.data.data)) {
            return { data: response.data.data };
        } else {
            throw new Error('Invalid response format from server');
        }
    } catch (error) {
        throw error;
    }
};

export const downloadDocument = async (documentId: string) => {
    try {
        const response = await api.get(`/documents/download/${documentId}`, {
            responseType: 'blob'
        });
        return response.data;
    } catch (error: any) {
        if (error.response?.data?.message) {
            throw new Error(error.response.data.message);
        }
        throw error;
    }
};

export const deleteDocument = async (documentId: string) => {
    try {
        const response = await api.delete(`/documents/${documentId}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

// Rent Entry interfaces
export interface RentEntry {
    id?: string;
    tenant: string | { fullName: string; firmName: string; id: string };
    rentMonth: string;
    building: string;
    floor: string;
    rentDetails: {
        cashRent: number;
        bankRent: number;
        roomRent: number;
        gst: number;
        gstAmount: number;
        totalRent: number;
        totalPayable: number;
    };
    paymentStatus: 'pending' | 'partial' | 'paid';
    createdAt?: string;
    updatedAt?: string;
}

// Rent Entry API functions
export const createRentEntry = async (rentEntryData: RentEntry) => {
    try {
        const response = await api.post('/rent-entries', rentEntryData);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getAllRentEntries = async () => {
    try {
        const response = await api.get('/rent-entries');
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getTenantRentEntries = async (tenantId: string) => {
    try {
        const response = await api.get(`/rent-entries/tenant/${tenantId}`);
        if (response.data.success && Array.isArray(response.data.data)) {
            return { data: response.data.data };
        } else {
            throw new Error('Invalid response format from server');
        }
    } catch (error) {
        throw error;
    }
};

export const updateRentEntry = async (id: string, rentEntryData: Partial<RentEntry>) => {
    try {
        const response = await api.put(`/rent-entries/${id}`, rentEntryData);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const deleteRentEntry = async (id: string) => {
    try {
        const response = await api.delete(`/rent-entries/${id}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

// Rent Increment interfaces
export interface RentIncrement {
    id?: string;
    tenant: string | Tenant;
    oldRent: number;
    newRent: number;
    incrementPercentage: number;
    incrementAmount: number;
    effectiveDate: string;
    appliedDate?: string;
    reason?: string;
    status: 'pending' | 'applied' | 'cancelled';
    oldRentDetails: {
        cashRent: number;
        bankRent: number;
        roomRent: number;
        gstAmount: number;
        totalRent: number;
        totalPayable: number;
    };
    newRentDetails: {
        cashRent: number;
        bankRent: number;
        roomRent: number;
        gstAmount: number;
        totalRent: number;
        totalPayable: number;
    };
    notes?: string;
    createdAt?: string;
    updatedAt?: string;
}

// Rent Increment API functions
export const getRentIncrements = async (params?: {
    tenant?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    building?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: string;
}) => {
    try {
        const response = await api.get('/rent-increments', { params });
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getUpcomingRentIncrements = async (daysAhead?: number) => {
    try {
        const response = await api.get('/rent-increments/upcoming', {
            params: { daysAhead }
        });
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const applyRentIncrement = async (data: {
    tenantId: string;
    incrementPercentage: number;
    effectiveDate?: string;
    reason?: string;
    notes?: string;
}) => {
    try {
        const response = await api.post('/rent-increments/apply', data);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getTenantRentIncrementHistory = async (tenantId: string) => {
    try {
        const response = await api.get(`/rent-increments/tenant/${tenantId}`);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getRentIncrementStats = async () => {
    try {
        const response = await api.get('/rent-increments/stats');
        return response.data;
    } catch (error) {
        throw error;
    }
};

export default api;