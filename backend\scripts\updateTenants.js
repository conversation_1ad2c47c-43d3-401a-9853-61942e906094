const mongoose = require('mongoose');
const Tenant = require('../models/Tenant');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/rent-guardian')
    .then(() => console.log('Connected to MongoDB'))
    .catch((err) => console.error('MongoDB connection error:', err));

async function updateTenantsWithIncrementDates() {
    try {
        // Get all tenants without nextIncrementDate
        const tenants = await Tenant.find({
            $or: [
                { nextIncrementDate: { $exists: false } },
                { nextIncrementDate: null }
            ]
        });

        console.log(`Found ${tenants.length} tenants without nextIncrementDate`);

        for (const tenant of tenants) {
            // Set nextIncrementDate to 1 year from rentStartDate or current date
            const baseDate = tenant.rentStartDate ? new Date(tenant.rentStartDate) : new Date();
            const nextIncrementDate = new Date(baseDate);
            nextIncrementDate.setFullYear(nextIncrementDate.getFullYear() + 1);

            // Update the tenant
            await Tenant.findByIdAndUpdate(tenant._id, {
                nextIncrementDate: nextIncrementDate,
                updatedAt: new Date()
            });

            console.log(`Updated ${tenant.fullName} - Next increment: ${nextIncrementDate.toISOString().split('T')[0]}`);
        }

        console.log('All tenants updated successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error updating tenants:', error);
        process.exit(1);
    }
}

updateTenantsWithIncrementDates();
