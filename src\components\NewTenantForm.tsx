import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card } from './ui/card';
import { Save, User, Building } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { createTenant, updateTenant, getTenant, Tenant } from '@/services/api';

interface NewTenantFormProps {
  editingTenantId?: string | null;
  onSuccess?: () => void;
}

export const NewTenantForm: React.FC<NewTenantFormProps> = ({ editingTenantId, onSuccess }) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const initialFormData = {
    fullName: '',
    firmName: '',
    phone: '',
    email: '',
    address: '',
    building: '',
    floor: '',
    rentDetails: {
      cashRent: '',
      bankRent: '',
      roomRent: '',
      gst: 18,
      gstAmount: 0,
      totalRent: 0,
      totalPayable: 0
    },
    deposit: '',
    rentStartDate: '',
    nextIncrementDate: '',
    notes: '',
    isActive: true
  };

  const [formData, setFormData] = useState<any>(initialFormData);

  const buildings = ['Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const floors = ['GF', '1st', '2nd', '3rd', '4th', '5th'];

  useEffect(() => {
    if (editingTenantId) {
      loadTenant();
    }
  }, [editingTenantId]);

  const loadTenant = async () => {
    try {
      setIsLoading(true);
      const response = await getTenant(editingTenantId!);

      // Format dates for HTML date inputs (YYYY-MM-DD format)
      const tenantData = response.data;
      const formattedData = {
        ...tenantData,
        rentStartDate: tenantData.rentStartDate ? new Date(tenantData.rentStartDate).toISOString().split('T')[0] : '',
        nextIncrementDate: tenantData.nextIncrementDate ? new Date(tenantData.nextIncrementDate).toISOString().split('T')[0] : ''
      };

      setFormData(formattedData);
    } catch (error: any) {
      console.error('Error loading tenant:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to load tenant details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRent = (data: typeof formData) => {
    // Convert empty strings to 0 for calculations
    const cashRent = data.rentDetails.cashRent === '' ? 0 : Number(data.rentDetails.cashRent);
    const bankRent = data.rentDetails.bankRent === '' ? 0 : Number(data.rentDetails.bankRent);
    const roomRent = data.rentDetails.roomRent === '' ? 0 : Number(data.rentDetails.roomRent);
    const gst = data.rentDetails.gst === '' ? 18 : Number(data.rentDetails.gst);

    // Calculate total rent (cash rent + bank rent only)
    const totalRent = cashRent + bankRent;

    // Calculate GST amount based on bank rent
    const gstAmount = Math.round(bankRent * (gst / 100));

    // Calculate total payable (total rent + GST only)
    const totalPayable = totalRent + gstAmount;

    return {
      ...data.rentDetails,
      gstAmount,
      totalRent,
      totalPayable
    };
  };

  const handleRentChange = (field: keyof typeof formData.rentDetails, value: string) => {
    const newFormData = {
      ...formData,
      rentDetails: {
        ...formData.rentDetails,
        [field]: value
      }
    };

    const calculatedRent = calculateRent(newFormData);
    setFormData({
      ...newFormData,
      rentDetails: calculatedRent
    });
  };

  const handleInputChange = (e: { target: { name: string; value: any; }; }) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (editingTenantId) {
        await updateTenant(editingTenantId, formData);
        toast({
          title: "Success",
          description: "Tenant updated successfully",
        });
        // Call onSuccess callback after successful update
        onSuccess?.();
      } else {
        await createTenant(formData);
        toast({
          title: "Success",
          description: "Tenant added successfully",
        });
        // Reset form after successful submission for new tenant
        setFormData(initialFormData);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to save tenant",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <div className="flex items-center space-x-2 mb-6">
              <User className="h-6 w-6 text-blue-600" />
              <h3 className="text-xl font-semibold">
                {editingTenantId ? 'Edit Tenant' : 'Add New Tenant'}
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h4 className="font-medium border-b pb-2">Personal Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fullName">Full Name *</Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      placeholder="Enter tenant's full name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="firmName">Firm/Company Name</Label>
                    <Input
                      id="firmName"
                      name="firmName"
                      value={formData.firmName}
                      onChange={handleInputChange}
                      placeholder="Enter firm or company name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Enter phone number"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter email address"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="address">Address *</Label>
                    <Input
                      id="address"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      placeholder="Enter tenant's address"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="building">Building *</Label>
                    <select
                      id="building"
                      name="building"
                      value={formData.building}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    >
                      <option value="">Select Building</option>
                      {buildings.map(building => (
                        <option key={building} value={building}>{building}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="floor">Floor *</Label>
                    <select
                      id="floor"
                      name="floor"
                      value={formData.floor}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    >
                      <option value="">Select Floor</option>
                      {floors.map(floor => (
                        <option key={floor} value={floor}>{floor}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="isActive">Status</Label>
                    <select
                      id="isActive"
                      name="isActive"
                      value={formData.isActive ? 'true' : 'false'}
                      onChange={(e) => handleInputChange({
                        target: {
                          name: 'isActive',
                          value: e.target.value === 'true'
                        }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                      <option value="true">Active</option>
                      <option value="false">Inactive</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Rent Information */}
              <div className="space-y-4">
                <h4 className="font-medium border-b pb-2">Rent Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="cashRent">Cash Rent (₹) *</Label>
                    <Input
                      id="cashRent"
                      type="number"
                      value={formData.rentDetails.cashRent}
                      onChange={(e) => handleRentChange('cashRent', e.target.value)}
                      placeholder="Enter cash rent"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="bankRent">Bank Rent (₹) *</Label>
                    <Input
                      id="bankRent"
                      type="number"
                      value={formData.rentDetails.bankRent}
                      onChange={(e) => handleRentChange('bankRent', e.target.value)}
                      placeholder="Enter bank rent"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="gst">GST (%)</Label>
                    <Input
                      id="gst"
                      type="number"
                      value={formData.rentDetails.gst}
                      onChange={(e) => handleRentChange('gst', e.target.value)}
                      placeholder="Enter GST percentage"
                    />
                  </div>
                  <div>
                    <Label htmlFor="roomRent">Room Rent (₹)</Label>
                    <Input
                      id="roomRent"
                      type="number"
                      value={formData.rentDetails.roomRent}
                      onChange={(e) => handleRentChange('roomRent', e.target.value)}
                      placeholder="Enter room rent"
                    />
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-semibold text-blue-800 dark:text-blue-200">Total Rent (Cash + Bank):</span>
                    <span className="text-xl font-bold text-blue-800 dark:text-blue-200">₹{formData.rentDetails.totalRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm text-blue-600 dark:text-blue-300">
                    <span>Total Payable (Total Rent + GST):</span>
                    <span className="font-medium">₹{formData.rentDetails.totalPayable.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm text-blue-600 dark:text-blue-300 mt-1">
                    <span>Room Rent (Separate):</span>
                    <span className="font-medium">₹{formData.rentDetails.roomRent.toLocaleString()}</span>
                  </div>
                </div>

                <div>
                  <Label htmlFor="deposit">Deposit Amount (₹) *</Label>
                  <Input
                    id="deposit"
                    type="number"
                    value={formData.deposit}
                    onChange={(e) => setFormData(prev => ({ ...prev, deposit: e.target.value }))}
                    placeholder="Enter deposit amount"
                    required
                    className="text-lg font-medium"
                  />
                </div>
              </div>

              {/* Date Information */}
              <div className="space-y-4">
                <h4 className="font-medium border-b pb-2">Rent Schedule</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="rentStartDate">Rent Start Date *</Label>
                    <Input
                      id="rentStartDate"
                      type="date"
                      value={formData.rentStartDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, rentStartDate: e.target.value }))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="nextIncrementDate">Next Increment Date</Label>
                    <Input
                      id="nextIncrementDate"
                      type="date"
                      value={formData.nextIncrementDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, nextIncrementDate: e.target.value }))}
                    />
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div>
                <Label htmlFor="notes">Notes</Label>
                <textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add any additional notes about the tenant..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-20 resize-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
              >
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? 'Saving...' : editingTenantId ? 'Update Tenant' : 'Add Tenant'}
              </Button>
            </form>
          </Card>
        </div>

        {/* Preview Card */}
        <div className="lg:col-span-1">
          <Card className="p-6 sticky top-8">
            <div className="flex items-center space-x-2 mb-4">
              <Building className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold">Tenant Summary</h3>
            </div>

            {formData.fullName ? (
              <div className="space-y-4">
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Personal Details</h4>
                  <p className="text-sm"><strong>Name:</strong> {formData.fullName}</p>
                  {formData.firmName && <p className="text-sm"><strong>Firm:</strong> {formData.firmName}</p>}
                  <p className="text-sm"><strong>Phone:</strong> {formData.phone}</p>
                  {formData.email && <p className="text-sm"><strong>Email:</strong> {formData.email}</p>}
                </div>

                {formData.building && (
                  <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Property</h4>
                    <p className="text-sm">{formData.building} - {formData.floor}</p>
                  </div>
                )}

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Cash Rent:</span>
                    <span className="font-medium">₹{formData.rentDetails.cashRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Bank Rent:</span>
                    <span className="font-medium">₹{formData.rentDetails.bankRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">GST ({formData.rentDetails.gst}%):</span>
                    <span className="font-medium">₹{formData.rentDetails.gstAmount.toLocaleString()}</span>
                  </div>
                  <hr className="my-3" />
                  <div className="flex justify-between text-lg">
                    <span className="font-semibold">Total Rent:</span>
                    <span className="font-bold text-blue-600 dark:text-blue-400">₹{formData.rentDetails.totalRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Total Payable (Inc. GST):</span>
                    <span className="font-medium text-green-600 dark:text-green-400">₹{formData.rentDetails.totalPayable.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm mt-2 border-t pt-2">
                    <span className="text-gray-600 dark:text-gray-400">Room Rent (Separate):</span>
                    <span className="font-medium text-orange-600 dark:text-orange-400">₹{formData.rentDetails.roomRent.toLocaleString()}</span>
                  </div>

                  {formData.deposit > 0 && (
                    <div className="flex justify-between border-t pt-3">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Deposit:</span>
                      <span className="font-medium">₹{formData.deposit.toLocaleString()}</span>
                    </div>
                  )}
                </div>

                {formData.notes && (
                  <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <h4 className="font-medium mb-2">Notes</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{formData.notes}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                Fill in tenant details to see summary
              </p>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};
