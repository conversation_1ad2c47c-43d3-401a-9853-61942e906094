
import React from 'react';
import { Calendar, TrendingUp, CheckCircle } from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';

const upcomingIncrements = [
  {
    id: 1,
    tenant: '<PERSON>',
    building: 'Ashirward Empro Park 5',
    floor: '2nd',
    currentRent: 26600,
    newRent: 27930,
    incrementDate: '2024-06-01',
    daysRemaining: 15,
  },
  {
    id: 2,
    tenant: '<PERSON>',
    building: 'Plot 5 Ashirward Empro',
    floor: '1st',
    currentRent: 22000,
    newRent: 23100,
    incrementDate: '2024-06-15',
    daysRemaining: 29,
  },
  {
    id: 3,
    tenant: '<PERSON><PERSON><PERSON>',
    building: 'Plot 6 Ashirward Empro',
    floor: 'GF',
    currentRent: 18500,
    newRent: 19425,
    incrementDate: '2024-07-01',
    daysRemaining: 45,
  }
];

export const RentIncrements: React.FC = () => {
  const handleApplyIncrement = (tenantId: number) => {
    console.log('Applying increment for tenant:', tenantId);
    // Here you would update the tenant's rent in your backend
    alert('Rent increment applied successfully!');
  };

  const getUrgencyColor = (daysRemaining: number) => {
    if (daysRemaining <= 7) return 'bg-red-100 text-red-800';
    if (daysRemaining <= 30) return 'bg-orange-100 text-orange-800';
    return 'bg-blue-100 text-blue-800';
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-orange-100 rounded-lg">
              <Calendar className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Due This Month</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Expected Increase</p>
              <p className="text-2xl font-bold text-gray-900">₹2,455</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Applied This Quarter</p>
              <p className="text-2xl font-bold text-gray-900">8</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Upcoming Increments Table */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Upcoming Rent Increments</h3>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tenant</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Property</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Rent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Rent (5%)</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Increment Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Remaining</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {upcomingIncrements.map((increment) => (
                <tr key={increment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">{increment.tenant}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{increment.building}</div>
                    <div className="text-sm text-gray-500">{increment.floor}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ₹{increment.currentRent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-green-600">
                      ₹{increment.newRent.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      (+₹{(increment.newRent - increment.currentRent).toLocaleString()})
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {increment.incrementDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUrgencyColor(increment.daysRemaining)}`}>
                      {increment.daysRemaining} days
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Button
                      onClick={() => handleApplyIncrement(increment.id)}
                      className="bg-blue-600 hover:bg-blue-700"
                      size="sm"
                    >
                      Apply Increment
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};
