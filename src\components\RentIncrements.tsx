import React, { useState, useEffect } from 'react';
import { Calendar, TrendingUp, CheckCircle, Plus, Edit, Filter, FileDown, ArrowUpDown, Search, AlertTriangle } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Badge } from './ui/badge';
import { useToast } from './ui/use-toast';
import { getTenants, updateTenant, Tenant, applyRentIncrement, getRentIncrementStats } from '../services/api';
import * as XLSX from 'xlsx';

interface RentIncrement {
  id: string;
  tenant: Tenant;
  currentRent: number;
  newRent: number;
  incrementPercentage: number;
  incrementDate: string;
  daysRemaining: number;
  status: 'upcoming' | 'due' | 'overdue' | 'applied';
  lastIncrementDate?: string;
  incrementHistory?: Array<{
    date: string;
    oldRent: number;
    newRent: number;
    percentage: number;
  }>;
}

interface IncrementStats {
  dueThisMonth: number;
  expectedIncrease: number;
  appliedThisQuarter: number;
  totalUpcoming: number;
  overdueCount: number;
}

export const RentIncrements: React.FC = () => {
  const { toast } = useToast();
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [rentIncrements, setRentIncrements] = useState<RentIncrement[]>([]);
  const [stats, setStats] = useState<IncrementStats>({
    dueThisMonth: 0,
    expectedIncrease: 0,
    appliedThisQuarter: 0,
    totalUpcoming: 0,
    overdueCount: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterBuilding, setFilterBuilding] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'tenant'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Load tenants and calculate increments
  useEffect(() => {
    loadTenantsAndCalculateIncrements();
  }, []);

  const loadTenantsAndCalculateIncrements = async () => {
    try {
      setIsLoading(true);
      const tenantsData = await getTenants();
      setTenants(tenantsData);

      const increments = calculateRentIncrements(tenantsData);
      setRentIncrements(increments);

      const calculatedStats = calculateStats(increments);
      setStats(calculatedStats);
    } catch (error) {
      console.error('Error loading tenants:', error);
      toast({
        title: "Error",
        description: "Failed to load tenant data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRentIncrements = (tenantsData: Tenant[]): RentIncrement[] => {
    const today = new Date();
    const increments: RentIncrement[] = [];

    tenantsData.forEach(tenant => {
      if (!tenant.isActive || !tenant.nextIncrementDate) return;

      const incrementDate = new Date(tenant.nextIncrementDate);
      const daysRemaining = Math.ceil((incrementDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

      // Determine status
      let status: 'upcoming' | 'due' | 'overdue' | 'applied' = 'upcoming';
      if (daysRemaining < 0) {
        status = 'overdue';
      } else if (daysRemaining <= 30) {
        status = 'due';
      }

      const currentRent = tenant.rentDetails.totalRent;
      const incrementPercentage = 5; // Default 5% increment
      const newRent = Math.round(currentRent * (1 + incrementPercentage / 100));

      increments.push({
        id: tenant.id || '',
        tenant,
        currentRent,
        newRent,
        incrementPercentage,
        incrementDate: tenant.nextIncrementDate,
        daysRemaining,
        status,
        lastIncrementDate: tenant.rentStartDate // This could be enhanced with actual last increment tracking
      });
    });

    return increments;
  };

  const calculateStats = (increments: RentIncrement[]): IncrementStats => {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    const monthStart = new Date(currentYear, currentMonth, 1);
    const monthEnd = new Date(currentYear, currentMonth + 1, 0);

    // Due This Month: Count increments that are due in current month
    const dueThisMonth = increments.filter(inc => {
      const incDate = new Date(inc.incrementDate);
      return incDate >= monthStart && incDate <= monthEnd;
    }).length;

    // Expected Increase: Sum of increment amounts for due and overdue entries
    const expectedIncrease = increments
      .filter(inc => {
        const incDate = new Date(inc.incrementDate);
        const daysRemaining = Math.ceil((incDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        return daysRemaining <= 30 || daysRemaining < 0; // Due or overdue
      })
      .reduce((sum, inc) => {
        const incrementAmount = inc.newRent - inc.currentRent;
        return sum + incrementAmount;
      }, 0);

    // Count overdue entries
    const overdueCount = increments.filter(inc => {
      const incDate = new Date(inc.incrementDate);
      return incDate < today;
    }).length;

    return {
      dueThisMonth,
      expectedIncrease,
      appliedThisQuarter: 0, // This would need to be tracked separately
      totalUpcoming: increments.length,
      overdueCount
    };
  };

  const getUrgencyColor = (daysRemaining: number) => {
    if (daysRemaining < 0) return 'bg-red-100 text-red-800 border-red-200';
    if (daysRemaining <= 7) return 'bg-orange-100 text-orange-800 border-orange-200';
    if (daysRemaining <= 30) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-blue-100 text-blue-800 border-blue-200';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'overdue':
        return <Badge variant="destructive" className="flex items-center gap-1"><AlertTriangle className="h-3 w-3" />Overdue</Badge>;
      case 'due':
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Due Soon</Badge>;
      case 'upcoming':
        return <Badge variant="outline">Upcoming</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const exportToExcel = () => {
    const wb = XLSX.utils.book_new();

    // Summary data
    const summaryData = [
      ['Rent Increments Summary Report'],
      ['Generated on:', new Date().toLocaleDateString()],
      [''],
      ['Statistics'],
      ['Due This Month', stats.dueThisMonth],
      ['Expected Increase', `₹${stats.expectedIncrease.toLocaleString()}`],
      ['Total Upcoming', stats.totalUpcoming],
      ['Overdue Count', stats.overdueCount],
      [''],
      ['Filters Applied'],
      ['Search Term', searchTerm || 'None'],
      ['Status Filter', filterStatus === 'all' ? 'All' : filterStatus],
      ['Building Filter', filterBuilding === 'all' ? 'All' : filterBuilding],
      ['Sort By', sortBy],
      ['Sort Order', sortOrder]
    ];

    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, summarySheet, 'Summary');

    // Rent increments data
    const incrementsData = [
      [
        'Tenant Name',
        'Firm Name',
        'Building',
        'Floor',
        'Phone',
        'Email',
        'Current Rent',
        'New Rent',
        'Increment Amount',
        'Increment %',
        'Increment Date',
        'Days Remaining',
        'Status',
        'Cash Rent',
        'Bank Rent',
        'Room Rent',
        'GST Amount',
        'Total Payable'
      ],
      ...filteredIncrements.map(increment => [
        increment.tenant.fullName,
        increment.tenant.firmName,
        increment.tenant.building,
        increment.tenant.floor,
        increment.tenant.phone,
        increment.tenant.email,
        increment.currentRent,
        increment.newRent,
        increment.newRent - increment.currentRent,
        increment.incrementPercentage,
        new Date(increment.incrementDate).toLocaleDateString(),
        increment.daysRemaining < 0 ? `${Math.abs(increment.daysRemaining)} days overdue` : `${increment.daysRemaining} days`,
        increment.status,
        increment.tenant.rentDetails.cashRent,
        increment.tenant.rentDetails.bankRent,
        increment.tenant.rentDetails.roomRent,
        increment.tenant.rentDetails.gstAmount,
        increment.tenant.rentDetails.totalPayable
      ])
    ];

    const incrementsSheet = XLSX.utils.aoa_to_sheet(incrementsData);
    XLSX.utils.book_append_sheet(wb, incrementsSheet, 'Rent Increments');

    // Building-wise summary
    const buildingStats = buildings.reduce((acc, building) => {
      const buildingIncrements = filteredIncrements.filter(inc => inc.tenant.building === building);
      const totalIncrease = buildingIncrements.reduce((sum, inc) => sum + (inc.newRent - inc.currentRent), 0);
      const overdueCount = buildingIncrements.filter(inc => inc.status === 'overdue').length;
      const dueCount = buildingIncrements.filter(inc => inc.status === 'due').length;

      acc.push([
        building,
        buildingIncrements.length,
        `₹${totalIncrease.toLocaleString()}`,
        overdueCount,
        dueCount,
        buildingIncrements.length - overdueCount - dueCount
      ]);
      return acc;
    }, [] as any[]);

    const buildingData = [
      ['Building-wise Rent Increment Summary'],
      [''],
      ['Building', 'Total Increments', 'Total Increase', 'Overdue', 'Due Soon', 'Upcoming'],
      ...buildingStats,
      [''],
      ['Total',
        filteredIncrements.length,
        `₹${filteredIncrements.reduce((sum, inc) => sum + (inc.newRent - inc.currentRent), 0).toLocaleString()}`,
        stats.overdueCount,
        filteredIncrements.filter(inc => inc.status === 'due').length,
        filteredIncrements.filter(inc => inc.status === 'upcoming').length
      ]
    ];

    const buildingSheet = XLSX.utils.aoa_to_sheet(buildingData);
    XLSX.utils.book_append_sheet(wb, buildingSheet, 'Building Summary');

    // Save the file
    const fileName = `rent_increments_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);

    toast({
      title: "Export Successful",
      description: `Rent increments data exported to ${fileName}`,
    });
  };

  // Filter and sort increments
  const filteredIncrements = rentIncrements
    .filter(increment => {
      const matchesSearch = increment.tenant.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        increment.tenant.building.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || increment.status === filterStatus;
      const matchesBuilding = filterBuilding === 'all' || increment.tenant.building === filterBuilding;
      return matchesSearch && matchesStatus && matchesBuilding;
    })
    .sort((a, b) => {
      let aValue, bValue;
      switch (sortBy) {
        case 'date':
          aValue = new Date(a.incrementDate).getTime();
          bValue = new Date(b.incrementDate).getTime();
          break;
        case 'amount':
          aValue = a.newRent - a.currentRent;
          bValue = b.newRent - b.currentRent;
          break;
        case 'tenant':
          aValue = a.tenant.fullName.toLowerCase();
          bValue = b.tenant.fullName.toLowerCase();
          break;
        default:
          return 0;
      }
      return sortOrder === 'asc' ? (aValue > bValue ? 1 : -1) : (aValue < bValue ? 1 : -1);
    });

  const buildings = [...new Set(tenants.map(t => t.building))];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-orange-100 dark:bg-orange-900/50 rounded-lg">
                <Calendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Due This Month</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.dueThisMonth}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 dark:bg-green-900/50 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Expected Increase</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">₹{stats.expectedIncrease.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                <CheckCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Upcoming</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalUpcoming}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-red-100 dark:bg-red-900/50 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Overdue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.overdueCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Rent Increment Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search" className="text-sm font-medium text-gray-700 dark:text-gray-300">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search tenants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter" className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</Label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger id="status-filter" className="h-10">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                  <SelectItem value="due">Due Soon</SelectItem>
                  <SelectItem value="upcoming">Upcoming</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="building-filter" className="text-sm font-medium text-gray-700 dark:text-gray-300">Building</Label>
              <Select value={filterBuilding} onValueChange={setFilterBuilding}>
                <SelectTrigger id="building-filter" className="h-10">
                  <SelectValue placeholder="All Buildings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Buildings</SelectItem>
                  {buildings.map(building => (
                    <SelectItem key={building} value={building}>{building}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sort-by" className="text-sm font-medium text-gray-700 dark:text-gray-300">Sort By</Label>
              <Select value={sortBy} onValueChange={(value: 'date' | 'amount' | 'tenant') => setSortBy(value)}>
                <SelectTrigger id="sort-by" className="h-10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Increment Date</SelectItem>
                  <SelectItem value="amount">Increment Amount</SelectItem>
                  <SelectItem value="tenant">Tenant Name</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Actions</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="h-10 px-3"
                >
                  <ArrowUpDown className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  className="h-10 px-3"
                  onClick={exportToExcel}
                  title="Export to Excel"
                >
                  <FileDown className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Increments Table */}
      <Card className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Upcoming Rent Increments ({filteredIncrements.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredIncrements.length === 0 ? (
            <div className="text-center py-12 text-gray-500 dark:text-gray-400">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No rent increments found</p>
              <p className="text-sm">Try adjusting your filters or check back later.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Tenant</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Property</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Current Rent</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">New Rent (5%)</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Increment Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredIncrements.map((increment) => (
                    <tr key={increment.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900 dark:text-gray-100">{increment.tenant.fullName}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{increment.tenant.firmName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">{increment.tenant.building}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{increment.tenant.floor}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        ₹{increment.currentRent.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-green-600 dark:text-green-400">
                          ₹{increment.newRent.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          (+₹{(increment.newRent - increment.currentRent).toLocaleString()})
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {new Date(increment.incrementDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col gap-1">
                          {getStatusBadge(increment.status)}
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUrgencyColor(increment.daysRemaining)}`}>
                            {increment.daysRemaining < 0 ? `${Math.abs(increment.daysRemaining)} days overdue` : `${increment.daysRemaining} days`}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
