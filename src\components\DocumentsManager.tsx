import React, { useState, useEffect } from 'react';
import { Upload, File, Download, Eye, Trash2, Plus, Loader2, Calendar } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardHeader, CardTitle, CardContent } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  getAllTenants,
  getAllDocuments,
  uploadDocument,
  downloadDocument,
  deleteDocument,
  type Tenant,
  type Document
} from '../services/api';
import { toast } from 'sonner';

const documentTypes = [
  'Aadhaar Card',
  'PAN Card',
  'Lease Agreement',
  'Police Verification',
  'Other Documents'
];

export const DocumentsManager: React.FC = () => {
  const [selectedTenant, setSelectedTenant] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [customLabel, setCustomLabel] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // State for data
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);

  // Loading states
  const [isLoadingTenants, setIsLoadingTenants] = useState(true);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(true);
  const [isUploading, setIsUploading] = useState(false);

  // Fetch tenants on component mount
  useEffect(() => {
    fetchTenants();
    fetchDocuments();
  }, []);

  const fetchTenants = async () => {
    try {
      setIsLoadingTenants(true);
      const response = await getAllTenants();
      setTenants(response.data || []);
    } catch (error: any) {
      console.error('Error fetching tenants:', error);
      toast.error(error.message || 'Failed to fetch tenants');
    } finally {
      setIsLoadingTenants(false);
    }
  };

  const fetchDocuments = async () => {
    try {
      setIsLoadingDocuments(true);
      const response = await getAllDocuments();
      setDocuments(response.data || []);
    } catch (error: any) {
      console.error('Error fetching documents:', error);
      toast.error(error.message || 'Failed to fetch documents');
    } finally {
      setIsLoadingDocuments(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size must be less than 10MB');
        return;
      }

      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Invalid file type. Only PDF, JPG, PNG, DOC, and DOCX files are allowed.');
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      setSelectedFile(files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !selectedTenant || !selectedType) {
      toast.error('Please select a file, tenant, and document type');
      return;
    }

    try {
      setIsUploading(true);

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('tenantId', selectedTenant);
      formData.append('documentType', selectedType === 'Other Documents' ? customLabel : selectedType);
      if (expiryDate) {
        formData.append('expiryDate', expiryDate);
      }

      await uploadDocument(formData);

      toast.success('Document uploaded successfully');

      // Reset form
      setSelectedFile(null);
      setSelectedTenant('');
      setSelectedType('');
      setCustomLabel('');
      setExpiryDate('');

      // Reset file input
      const fileInput = document.getElementById('fileUpload') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }

      // Refresh documents list
      await fetchDocuments();

    } catch (error: any) {
      console.error('Error uploading document:', error);
      toast.error(error.message || 'Failed to upload document');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = async (documentId: string, fileName: string) => {
    try {
      const blob = await downloadDocument(documentId);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Document downloaded successfully');
    } catch (error: any) {
      console.error('Error downloading document:', error);
      toast.error(error.message || 'Failed to download document');
    }
  };

  const handleDelete = async (documentId: string) => {
    if (!window.confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      await deleteDocument(documentId);
      toast.success('Document deleted successfully');
      await fetchDocuments();
    } catch (error: any) {
      console.error('Error deleting document:', error);
      toast.error(error.message || 'Failed to delete document');
    }
  };

  const getTenantName = (tenantId: string | { fullName: string }) => {
    if (typeof tenantId === 'object' && tenantId.fullName) {
      return tenantId.fullName;
    }
    const tenant = tenants.find(t => t.id === tenantId);
    return tenant ? tenant.fullName : 'Unknown Tenant';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Upload Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="tenant" className="text-gray-700 dark:text-gray-300">Select Tenant *</Label>
              {isLoadingTenants ? (
                <div className="w-full h-10 px-3 flex items-center border rounded-md border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-800">
                  <Loader2 className="h-4 w-4 animate-spin mr-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-sm text-gray-500 dark:text-gray-400">Loading tenants...</span>
                </div>
              ) : (
                <Select value={selectedTenant} onValueChange={setSelectedTenant}>
                  <SelectTrigger className="w-full bg-white dark:bg-gray-800">
                    <SelectValue placeholder="Choose tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    {tenants.map(tenant => (
                      <SelectItem key={tenant.id} value={tenant.id}>{tenant.fullName}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="documentType" className="text-gray-700 dark:text-gray-300">Document Type *</Label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-full bg-white dark:bg-gray-800">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedType === 'Other Documents' && (
              <div className="space-y-2">
                <Label htmlFor="customLabel" className="text-gray-700 dark:text-gray-300">Custom Label *</Label>
                <Input
                  id="customLabel"
                  value={customLabel}
                  onChange={(e) => setCustomLabel(e.target.value)}
                  placeholder="Enter document label"
                  className="bg-white dark:bg-gray-800"
                  required
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="expiryDate" className="text-gray-700 dark:text-gray-300">Expiry Date (Optional)</Label>
              <div className="relative">
                <Input
                  id="expiryDate"
                  type="date"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  className="bg-white dark:bg-gray-800 [color-scheme:dark]"
                />
                <Calendar className="absolute right-3 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div
              className="border-2 border-dashed border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 rounded-lg p-8 transition-colors cursor-pointer bg-gray-50 dark:bg-gray-800/50"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => document.getElementById('fileUpload')?.click()}
            >
              <input
                type="file"
                id="fileUpload"
                onChange={handleFileSelect}
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                className="hidden"
              />
              <div className="text-center">
                <Upload className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {selectedFile ? selectedFile.name : 'Upload Document'}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Drag and drop or click to select files</p>
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 10MB)</p>
              </div>
            </div>

            {selectedFile && (
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-800">
                <div className="flex items-center space-x-3">
                  <File className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{selectedFile.name}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(selectedFile.size)}</p>
                  </div>
                </div>
                <Button
                  onClick={handleUpload}
                  disabled={isUploading || !selectedTenant || !selectedType || (selectedType === 'Other Documents' && !customLabel)}
                  className="flex items-center space-x-2"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Uploading...</span>
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4" />
                      <span>Upload Document</span>
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Document Library</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingDocuments ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mr-3 text-gray-400 dark:text-gray-500" />
              <span className="text-gray-600 dark:text-gray-400">Loading documents...</span>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-12">
              <File className="h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 font-medium">No documents uploaded yet</p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">Upload your first document using the form above</p>
            </div>
          ) : (
            <div className="relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 dark:bg-gray-800/50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tenant</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Document Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">File Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">File Size</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Upload Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Expiry Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-800 bg-white dark:bg-gray-900">
                    {documents.map((doc) => (
                      <tr key={doc.id} className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900 dark:text-gray-100">{getTenantName(doc.tenant)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <File className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                            <span className="text-gray-900 dark:text-gray-100">{doc.documentType}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-gray-900 dark:text-gray-100">{doc.fileName}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-gray-600 dark:text-gray-400">{formatFileSize(doc.fileSize)}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-gray-600 dark:text-gray-400">{formatDate(doc.uploadDate)}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {doc.expiryDate ? (
                            <span className={`px-2 py-1 text-xs rounded-full inline-flex items-center ${new Date(doc.expiryDate) < new Date()
                              ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                              : 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                              }`}>
                              {formatDate(doc.expiryDate)}
                            </span>
                          ) : (
                            <span className="text-gray-400 dark:text-gray-500">No expiry</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-3">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownload(doc.id!, doc.fileName)}
                              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
                              title="Download document"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(doc.id!)}
                              className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                              title="Delete document"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
