import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Eye, Download, Trash2, Upload, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
    getAllTenants,
    getAllDocuments,
    uploadDocument,
    downloadDocument,
    deleteDocument,
    Tenant,
    Document
} from '@/services/api';
import { format } from 'date-fns';

const documentTypes = [
    'Aadhaar Card',
    'PAN Card',
    'Lease Agreement',
    'Police Verification',
    'Company ID',
    'Other'
];

export const DocumentManager = () => {
    const { toast } = useToast();
    const [isLoading, setIsLoading] = useState(false);
    const [tenants, setTenants] = useState<Tenant[]>([]);
    const [documents, setDocuments] = useState<Document[]>([]);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [formData, setFormData] = useState({
        tenantId: '',
        documentType: '',
        expiryDate: ''
    });

    useEffect(() => {
        loadTenants();
        loadDocuments();
    }, []);

    const loadTenants = async () => {
        try {
            setIsLoading(true);
            const response = await getAllTenants();
            if (response.data) {
                setTenants(response.data);
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.message || "Failed to load tenants",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const loadDocuments = async () => {
        try {
            setIsLoading(true);
            const response = await getAllDocuments();
            if (response.data) {
                setDocuments(response.data);
            }
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.message || "Failed to load documents",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setSelectedFile(e.target.files[0]);
        }
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        const files = e.dataTransfer.files;
        if (files && files[0]) {
            setSelectedFile(files[0]);
        }
    };

    const handleUpload = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!selectedFile || !formData.tenantId || !formData.documentType) {
            toast({
                title: "Error",
                description: "Please fill in all required fields and select a file",
                variant: "destructive",
            });
            return;
        }

        try {
            setIsLoading(true);
            const uploadFormData = new FormData();
            uploadFormData.append('file', selectedFile);
            uploadFormData.append('tenantId', formData.tenantId);
            uploadFormData.append('documentType', formData.documentType);
            if (formData.expiryDate) {
                uploadFormData.append('expiryDate', formData.expiryDate);
            }

            await uploadDocument(uploadFormData);
            toast({
                title: "Success",
                description: "Document uploaded successfully",
            });

            // Reset form and reload documents
            setSelectedFile(null);
            setFormData({
                tenantId: '',
                documentType: '',
                expiryDate: ''
            });
            if (e.target instanceof HTMLFormElement) {
                e.target.reset();
            }
            loadDocuments();
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.message || "Failed to upload document",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleDownload = async (documentId: string, fileName: string) => {
        try {
            setIsLoading(true);
            const blob = await downloadDocument(documentId);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.message || "Failed to download document",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleDelete = async (documentId: string) => {
        if (!window.confirm('Are you sure you want to delete this document?')) {
            return;
        }

        try {
            setIsLoading(true);
            await deleteDocument(documentId);
            toast({
                title: "Success",
                description: "Document deleted successfully",
            });
            loadDocuments();
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.message || "Failed to delete document",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <div className="space-y-6">
            <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Upload Documents</h3>
                <form onSubmit={handleUpload} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Label htmlFor="tenant">Select Tenant *</Label>
                            <select
                                id="tenant"
                                value={formData.tenantId}
                                onChange={(e) => setFormData(prev => ({ ...prev, tenantId: e.target.value }))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                required
                            >
                                <option value="">Choose tenant</option>
                                {tenants.map((tenant) => (
                                    <option key={tenant.id} value={tenant.id}>
                                        {tenant.fullName}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <Label htmlFor="documentType">Document Type *</Label>
                            <select
                                id="documentType"
                                value={formData.documentType}
                                onChange={(e) => setFormData(prev => ({ ...prev, documentType: e.target.value }))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                required
                            >
                                <option value="">Select type</option>
                                {documentTypes.map((type) => (
                                    <option key={type} value={type}>
                                        {type}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <Label htmlFor="expiryDate">Expiry Date (Optional)</Label>
                            <Input
                                id="expiryDate"
                                type="date"
                                value={formData.expiryDate}
                                onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
                            />
                        </div>
                    </div>

                    <div
                        className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-colors"
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                        onClick={() => document.getElementById('fileInput')?.click()}
                    >
                        <div className="flex flex-col items-center justify-center">
                            <Upload className="h-12 w-12 text-gray-400 mb-4" />
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                Drag and drop or click to select files
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 10MB)
                            </p>
                            <Input
                                id="fileInput"
                                type="file"
                                className="hidden"
                                onChange={handleFileChange}
                                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                required
                            />
                            {selectedFile && (
                                <div className="mt-4 text-sm text-blue-600 dark:text-blue-400">
                                    Selected: {selectedFile.name}
                                </div>
                            )}
                        </div>
                    </div>

                    <Button
                        type="submit"
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        disabled={isLoading}
                    >
                        {isLoading ? 'Processing...' : 'Upload Document'}
                    </Button>
                </form>
            </Card>

            <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Document Library</h3>
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Tenant</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Document Type</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">File Name</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Upload Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expiry Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {documents.map((doc) => (
                                <tr key={doc.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                                            {tenants.find(t => t.id === doc.tenant)?.fullName || 'Unknown'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">{doc.documentType}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">{doc.fileName}</div>
                                        <div className="text-xs text-gray-500">{formatFileSize(doc.fileSize)}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">
                                            {new Date(doc.uploadDate).toLocaleDateString()}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900 dark:text-white">
                                            {doc.expiryDate ? new Date(doc.expiryDate).toLocaleDateString() : 'No expiry'}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex space-x-2">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleDownload(doc.id!, doc.fileName)}
                                                title="Download"
                                                disabled={isLoading}
                                            >
                                                <Download className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleDelete(doc.id!)}
                                                title="Delete"
                                                className="text-red-600 hover:text-red-700"
                                                disabled={isLoading}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                            {documents.length === 0 && (
                                <tr>
                                    <td colSpan={6} className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                                        No documents uploaded yet
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </Card>
        </div>
    );
}; 