const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const RentEntry = require('../models/RentEntry');
const Tenant = require('../models/Tenant');
const { protect } = require('../middleware/auth');

// Get all rent entries
router.get('/', protect, async (req, res) => {
    try {
        const rentEntries = await RentEntry.find({ createdBy: req.user.id })
            .populate('tenant', 'fullName firmName')
            .sort({ rentMonth: -1 });

        res.json({
            success: true,
            data: rentEntries
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Get rent entries by tenant
router.get('/tenant/:tenantId', protect, async (req, res) => {
    try {
        const rentEntries = await RentEntry.find({
            tenant: req.params.tenantId,
            createdBy: req.user.id
        }).sort({ rentMonth: -1 });

        res.json({
            success: true,
            data: rentEntries
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Create rent entry
router.post('/', protect, [
    body('tenant').notEmpty().withMessage('Tenant ID is required'),
    body('rentMonth').notEmpty().withMessage('Rent month is required'),
    body('building').notEmpty().withMessage('Building is required'),
    body('floor').notEmpty().withMessage('Floor is required'),
    body('rentDetails.cashRent').isNumeric().withMessage('Cash rent must be a number'),
    body('rentDetails.bankRent').isNumeric().withMessage('Bank rent must be a number'),
    body('rentDetails.roomRent').isNumeric().withMessage('Room rent must be a number'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const {
            tenant,
            rentMonth,
            building,
            floor,
            rentDetails,
            paymentStatus
        } = req.body;

        // Calculate GST (18% on bank rent)
        const gstAmount = Math.round(rentDetails.bankRent * (rentDetails.gst || 18) / 100);

        // Calculate total rent (cash rent + bank rent + room rent)
        const totalRent = rentDetails.cashRent + rentDetails.bankRent + rentDetails.roomRent;

        // Calculate total payable (total rent + GST)
        const totalPayable = totalRent + gstAmount;

        const rentEntry = await RentEntry.create({
            tenant,
            rentMonth,
            building,
            floor,
            rentDetails: {
                ...rentDetails,
                gstAmount,
                totalRent,
                totalPayable
            },
            paymentStatus: paymentStatus || 'pending',
            createdBy: req.user.id
        });

        await rentEntry.populate('tenant', 'fullName firmName');

        res.status(201).json({
            success: true,
            data: rentEntry
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Update rent entry
router.put('/:id', protect, async (req, res) => {
    try {
        const {
            rentDetails,
            paymentStatus
        } = req.body;

        // Calculate GST (18% on bank rent)
        const gstAmount = Math.round(rentDetails.bankRent * (rentDetails.gst || 18) / 100);

        // Calculate total rent (cash rent + bank rent + room rent)
        const totalRent = rentDetails.cashRent + rentDetails.bankRent + rentDetails.roomRent;

        // Calculate total payable (total rent + GST)
        const totalPayable = totalRent + gstAmount;

        let rentEntry = await RentEntry.findOne({
            _id: req.params.id,
            createdBy: req.user.id
        });

        if (!rentEntry) {
            return res.status(404).json({ message: 'Rent entry not found' });
        }

        rentEntry = await RentEntry.findByIdAndUpdate(
            req.params.id,
            {
                rentDetails: {
                    ...rentDetails,
                    gstAmount,
                    totalRent,
                    totalPayable
                },
                paymentStatus: paymentStatus || rentEntry.paymentStatus
            },
            { new: true }
        ).populate('tenant', 'fullName firmName');

        res.json({
            success: true,
            data: rentEntry
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

// Delete rent entry
router.delete('/:id', protect, async (req, res) => {
    try {
        const rentEntry = await RentEntry.findOne({
            _id: req.params.id,
            createdBy: req.user.id
        });

        if (!rentEntry) {
            return res.status(404).json({ message: 'Rent entry not found' });
        }

        await RentEntry.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            data: {}
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
});

module.exports = router;