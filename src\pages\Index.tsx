import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '../components/DashboardLayout';
import { DashboardOverview } from '../components/DashboardOverview';
import TenantTable from '../components/TenantTable';
import { TenantDetails } from '../components/TenantDetails';
import { RentEntryForm } from '../components/RentEntryForm';
import { AllRentEntries } from '../components/AllRentEntries';
import { NewTenantForm } from '../components/NewTenantForm';
import { DocumentsManager } from '../components/DocumentsManager';
import { RentIncrements } from '../components/RentIncrements';
import { Reports } from '../components/Reports';
import { Settings } from '../components/Settings';
import { AdminLogin } from '../components/AdminLogin';
import { logout, getCurrentUser } from '../services/api';
import { useToast } from '@/hooks/use-toast';

const Index = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);
  const [editingTenantId, setEditingTenantId] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    if (token && storedUser) {
      setUser(JSON.parse(storedUser));
      setIsLoggedIn(true);

      // Verify token validity
      getCurrentUser()
        .catch(() => {
          handleLogout();
        });
    }
  }, []);

  const handleLogin = (userData: any) => {
    setUser(userData);
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    logout();
    setIsLoggedIn(false);
    setUser(null);
    setActiveTab('dashboard');
    setSelectedTenantId(null);
    setEditingTenantId(null);
    toast({
      title: "Logged out",
      description: "You have been logged out successfully",
    });
  };

  const handleAddTenant = () => {
    setActiveTab('add-tenant');
    setEditingTenantId(null);
  };

  const handleViewTenant = (tenantId: string) => {
    if (!tenantId) {
      toast({
        title: "Error",
        description: "Invalid tenant ID",
        variant: "destructive",
      });
      return;
    }
    setSelectedTenantId(tenantId);
    setActiveTab('tenant-details');
  };

  const handleEditTenant = (tenantId: string) => {
    setEditingTenantId(tenantId);
    setActiveTab('add-tenant');
  };

  const handleBackToTenants = () => {
    setSelectedTenantId(null);
    setEditingTenantId(null);
    setActiveTab('tenants');
  };

  if (!isLoggedIn) {
    return <AdminLogin onLogin={handleLogin} />;
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <>
            <DashboardOverview />
            <TenantTable
              onViewTenant={handleViewTenant}
              onEditTenant={handleEditTenant}
            />
          </>
        );
      case 'add-rent':
        return <RentEntryForm />;
      case 'all-rent-entries':
        return <AllRentEntries />;
      case 'add-tenant':
        return <NewTenantForm
          editingTenantId={editingTenantId}
          onSuccess={() => {
            setEditingTenantId(null);
            setActiveTab('tenants');
          }}
        />;
      case 'tenants':
        return (
          <TenantTable
            showFilters={true}
            onAddTenant={handleAddTenant}
            onViewTenant={handleViewTenant}
            onEditTenant={handleEditTenant}
          />
        );
      case 'tenant-details':
        return selectedTenantId ? (
          <TenantDetails
            tenantId={selectedTenantId}
            onBack={handleBackToTenants}
            onEdit={() => handleEditTenant(selectedTenantId)}
          />
        ) : null;
      case 'documents':
        return <DocumentsManager />;
      case 'rent-increments':
        return <RentIncrements />;
      case 'reports':
        return <Reports />;
      case 'settings':
        return <Settings />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <DashboardLayout
      activeTab={activeTab}
      onTabChange={setActiveTab}
      onLogout={handleLogout}
    >
      {renderContent()}
    </DashboardLayout>
  );
};

export default Index;
