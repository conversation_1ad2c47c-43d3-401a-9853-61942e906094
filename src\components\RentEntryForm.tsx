import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent } from './ui/card';
import { Calculator, Save, User, Building, Building2, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  getAllTenants,
  getTenant,
  createRentEntry,
  getRentEntriesByMonth,
  type Tenant,
  type RentEntry
} from '@/services/api';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Badge } from './ui/badge';

export const RentEntryForm: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [rentEntries, setRentEntries] = useState<RentEntry[]>([]);
  const [isLoadingEntries, setIsLoadingEntries] = useState(false);

  const [formData, setFormData] = useState<Partial<RentEntry>>({
    rentMonth: new Date().toISOString().slice(0, 7), // Current month YYYY-MM
    building: '',
    floor: 'GF',
    rentDetails: {
      cashRent: 0,
      bankRent: 0,
      roomRent: 0,
      gst: 18, // Fixed GST at 18%
      gstAmount: 0,
      totalRent: 0,
      totalPayable: 0
    },
    paymentStatus: 'paid'
  });

  const buildings = ['Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const floors = ['GF', '1st', '2nd', '3rd', '4th', '5th'];

  useEffect(() => {
    fetchTenants();
  }, []);

  useEffect(() => {
    if (formData.rentMonth) {
      fetchRentEntriesByMonth(formData.rentMonth);
    }
  }, [formData.rentMonth]);

  const fetchTenants = async () => {
    try {
      setIsLoading(true);
      const response = await getAllTenants();
      setTenants(response.data || []);
    } catch (error: any) {
      console.error('Error fetching tenants:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch tenants",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRentEntriesByMonth = async (month: string) => {
    try {
      setIsLoadingEntries(true);
      const response = await getRentEntriesByMonth(month);
      setRentEntries(response.data || []);
    } catch (error: any) {
      console.error('Error fetching rent entries:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch rent entries",
        variant: "destructive",
      });
    } finally {
      setIsLoadingEntries(false);
    }
  };

  const calculateRent = (data: typeof formData) => {
    if (!data.rentDetails) return data;

    // Convert values to numbers and handle empty strings
    const cashRent = Number(data.rentDetails.cashRent) || 0;
    const bankRent = Number(data.rentDetails.bankRent) || 0;
    const roomRent = Number(data.rentDetails.roomRent) || 0;
    const gst = 18; // Fixed GST at 18%

    // Calculate GST amount based on bank rent
    const gstAmount = Math.round(bankRent * (gst / 100));

    // Calculate total rent (cash rent + bank rent only)
    const totalRent = cashRent + bankRent;

    // Calculate total payable (total rent + GST)
    const totalPayable = totalRent + gstAmount;

    return {
      ...data,
      rentDetails: {
        ...data.rentDetails,
        cashRent,
        bankRent,
        roomRent,
        gst,
        gstAmount,
        totalRent,
        totalPayable
      }
    };
  };

  const handleBuildingFloorChange = async (building: string, floor: string) => {
    // Find tenant for this building and floor
    const tenant = tenants.find(t => t.building === building && t.floor === floor);

    if (tenant) {
      setFormData(prev => ({
        ...prev,
        tenant: tenant.id,
        building,
        floor,
        rentDetails: {
          ...tenant.rentDetails,
          gst: tenant.rentDetails.gst || 18 // Use tenant's GST if available, otherwise default to 18
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        tenant: undefined,
        building,
        floor,
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        }
      }));
    }
  };

  const handleRentChange = (field: keyof typeof formData.rentDetails, value: number) => {
    const newFormData = {
      ...formData,
      rentDetails: {
        ...formData.rentDetails!,
        [field]: value
      }
    };

    const calculatedRent = calculateRent(newFormData);
    setFormData(calculatedRent);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.tenant) {
      toast({
        title: "Error",
        description: "No tenant found for this building and floor",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      // Format the rentMonth to include day (first day of the month)
      const rentEntryData = {
        ...formData,
        rentMonth: `${formData.rentMonth}-01`,
      } as RentEntry;

      const response = await createRentEntry(rentEntryData);

      if (!response.success) {
        toast({
          title: "Error",
          description: response.message || "Failed to save rent entry",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: `Rent entry for ${formData.rentMonth} saved successfully!`,
      });

      // Refresh rent entries for the current month
      if (formData.rentMonth) {
        fetchRentEntriesByMonth(formData.rentMonth);
      }

      // Reset form with initial values to maintain controlled inputs
      setFormData({
        rentMonth: new Date().toISOString().slice(0, 7),
        building: '',
        floor: 'GF',
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        },
        paymentStatus: 'paid'
      });
    } catch (error: any) {
      console.error('Error saving rent entry:', error);
      toast({
        title: "Error",
        description: error.response?.data?.message || error.message || "Failed to save rent entry",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getTenantDetails = () => {
    if (!formData.tenant) return null;
    const tenant = tenants.find(t => t.id === formData.tenant);
    return tenant;
  };

  const getPendingRents = () => {
    if (!formData.rentMonth) return [];

    const activeTenants = tenants.filter(tenant => tenant.isActive);
    const paidTenantIds = rentEntries
      .filter(entry => entry.paymentStatus === 'paid')
      .map(entry => typeof entry.tenant === 'string' ? entry.tenant : entry.tenant.id);

    return activeTenants.filter(tenant => !paidTenantIds.includes(tenant.id));
  };

  const getPaidRents = () => {
    return rentEntries.filter(entry => entry.paymentStatus === 'paid');
  };

  const getPartialRents = () => {
    return rentEntries.filter(entry => entry.paymentStatus === 'partial');
  };

  const getOverdueRents = () => {
    return rentEntries.filter(entry => entry.paymentStatus === 'pending');
  };

  const pendingRents = getPendingRents();
  const paidRents = getPaidRents();
  const partialRents = getPartialRents();
  const overdueRents = getOverdueRents();

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Monthly Rent Entry</h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Month Selection */}
              <div>
                <Label htmlFor="rentMonth">Rent Month *</Label>
                <Input
                  id="rentMonth"
                  type="month"
                  value={formData.rentMonth}
                  onChange={(e) => setFormData(prev => ({ ...prev, rentMonth: e.target.value }))}
                  required
                  className="h-10 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 [color-scheme:light] dark:[color-scheme:dark]"
                />
              </div>

              {/* Property Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="building">Building *</Label>
                  <Select value={formData.building} onValueChange={(value) => handleBuildingFloorChange(value, formData.floor!)}>
                    <SelectTrigger id="building" className="w-full">
                      <SelectValue placeholder="Select Building" />
                    </SelectTrigger>
                    <SelectContent>
                      {buildings.map(building => (
                        <SelectItem key={building} value={building}>{building}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="floor">Floor *</Label>
                  <Select value={formData.floor} onValueChange={(value) => handleBuildingFloorChange(formData.building!, value)}>
                    <SelectTrigger id="floor" className="w-full">
                      <SelectValue placeholder="Select Floor" />
                    </SelectTrigger>
                    <SelectContent>
                      {floors.map(floor => (
                        <SelectItem key={floor} value={floor}>{floor}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Auto-populated Tenant Information */}
              {formData.tenant && (
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg space-y-3">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Tenant Details</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Name:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.fullName || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Firm:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.firmName || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Phone:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.phone || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Email:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.email || '-'}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Rent Amount */}
              {formData.building && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Calculator className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Rent Details - {formData.rentMonth}</h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <Label htmlFor="cashRent" className="text-gray-700 dark:text-gray-300">Cash Rent (₹) *</Label>
                      <Input
                        id="cashRent"
                        type="number"
                        value={formData.rentDetails?.cashRent || ''}
                        onChange={(e) => handleRentChange('cashRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                    <div>
                      <Label htmlFor="bankRent" className="text-gray-700 dark:text-gray-300">Bank Rent (₹) *</Label>
                      <Input
                        id="bankRent"
                        type="number"
                        value={formData.rentDetails?.bankRent || ''}
                        onChange={(e) => handleRentChange('bankRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                    <div>
                      <Label htmlFor="gst" className="text-gray-700 dark:text-gray-300">GST (%)</Label>
                      <Input
                        id="gst"
                        type="number"
                        value={18}
                        disabled
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 cursor-not-allowed bg-gray-100"
                      />
                    </div>
                    <div>
                      <Label htmlFor="roomRent" className="text-gray-700 dark:text-gray-300">Room Rent (₹)</Label>
                      <Input
                        id="roomRent"
                        type="number"
                        value={formData.rentDetails?.roomRent || ''}
                        onChange={(e) => handleRentChange('roomRent', Number(e.target.value))}
                        placeholder="0"
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                  </div>
                </div>
              )}

              {formData.building && (
                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3"
                  disabled={isLoading}
                >
                  <Save className="h-5 w-5 mr-2" />
                  {isLoading ? 'Saving...' : `Save Rent Entry for ${formData.rentMonth}`}
                </Button>
              )}
            </form>
          </Card>
        </div>

        {/* Preview Card */}
        <div className="lg:col-span-1">
          <Card className="sticky top-8 bg-white dark:bg-gray-800 border-0">
            <CardContent className="p-6 space-y-6">
              <div className="flex items-center gap-2">
                <Calculator className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <h3 className="font-medium text-gray-900 dark:text-gray-100">Rent Summary</h3>
              </div>

              {formData.building ? (
                <div className="space-y-6">
                  {/* Property Details */}
                  <div className="bg-blue-50/50 dark:bg-blue-900/10 rounded-lg p-4 space-y-1">
                    <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400 mb-2">
                      <Building2 className="h-4 w-4" />
                      <span className="font-medium">Property</span>
                    </div>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{formData.building} - {formData.floor}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Month: {formData.rentMonth}</p>
                  </div>

                  {/* Rent Breakdown */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Cash Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.cashRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Bank Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.bankRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">GST ({formData.rentDetails?.gst}%):</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.gstAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Room Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.roomRent.toLocaleString()}</span>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900 dark:text-gray-100">Total Rent:</span>
                        <span className="font-bold text-blue-600 dark:text-blue-400">₹{formData.rentDetails?.totalRent.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Total Payable (Inc. GST):</span>
                        <span className="font-medium text-green-600 dark:text-green-400">₹{formData.rentDetails?.totalPayable.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Tenant Details */}
                  {getTenantDetails() && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3 border border-gray-100 dark:border-gray-700">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">Tenant</h4>
                      <div className="space-y-2">
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Name:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.fullName}</p>
                        </div>
                        {getTenantDetails()?.firmName && (
                          <div className="space-y-1">
                            <p className="text-sm text-gray-500 dark:text-gray-400">Firm:</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.firmName}</p>
                          </div>
                        )}
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Phone:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.phone}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Email:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.email || '-'}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building2 className="h-8 w-8 mx-auto mb-3 text-gray-400 dark:text-gray-600" />
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    Select building and floor to view rent details
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Rent Lists Section */}
      {formData.rentMonth && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Rent Status for {new Date(formData.rentMonth + '-01').toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </h2>
            {isLoadingEntries && (
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                Loading...
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
            {/* Pending Rents */}
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">Pending</h3>
                  </div>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                    {pendingRents.length}
                  </Badge>
                </div>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {pendingRents.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                      No pending rents
                    </p>
                  ) : (
                    pendingRents.map((tenant) => (
                      <div key={tenant.id} className="p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-lg border border-yellow-200 dark:border-yellow-800/30">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">{tenant.fullName}</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {tenant.building} - {tenant.floor}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold text-yellow-700 dark:text-yellow-400">
                              ₹{tenant.rentDetails.totalPayable.toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Cash: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{tenant.rentDetails.cashRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Bank: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{tenant.rentDetails.bankRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Room: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{tenant.rentDetails.roomRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">GST: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{tenant.rentDetails.gstAmount.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Paid Rents */}
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">Paid</h3>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    {paidRents.length}
                  </Badge>
                </div>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {paidRents.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                      No paid rents
                    </p>
                  ) : (
                    paidRents.map((entry) => (
                      <div key={entry.id} className="p-3 bg-green-50 dark:bg-green-900/10 rounded-lg border border-green-200 dark:border-green-800/30">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                              {typeof entry.tenant === 'string' ? 'Unknown' : entry.tenant.fullName}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {entry.building} - {entry.floor}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold text-green-700 dark:text-green-400">
                              ₹{entry.rentDetails.totalPayable.toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Cash: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.cashRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Bank: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.bankRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Room: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.roomRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">GST: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.gstAmount.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Partial Rents */}
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">Partial</h3>
                  </div>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400">
                    {partialRents.length}
                  </Badge>
                </div>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {partialRents.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                      No partial payments
                    </p>
                  ) : (
                    partialRents.map((entry) => (
                      <div key={entry.id} className="p-3 bg-orange-50 dark:bg-orange-900/10 rounded-lg border border-orange-200 dark:border-orange-800/30">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                              {typeof entry.tenant === 'string' ? 'Unknown' : entry.tenant.fullName}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {entry.building} - {entry.floor}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold text-orange-700 dark:text-orange-400">
                              ₹{entry.rentDetails.totalPayable.toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Cash: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.cashRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Bank: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.bankRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Room: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.roomRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">GST: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.gstAmount.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Overdue Rents */}
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">Overdue</h3>
                  </div>
                  <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                    {overdueRents.length}
                  </Badge>
                </div>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {overdueRents.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
                      No overdue rents
                    </p>
                  ) : (
                    overdueRents.map((entry) => (
                      <div key={entry.id} className="p-3 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800/30">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                              {typeof entry.tenant === 'string' ? 'Unknown' : entry.tenant.fullName}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {entry.building} - {entry.floor}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold text-red-700 dark:text-red-400">
                              ₹{entry.rentDetails.totalPayable.toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Cash: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.cashRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Bank: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.bankRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Room: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.roomRent.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">GST: </span>
                            <span className="text-gray-900 dark:text-gray-100">₹{entry.rentDetails.gstAmount.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};
