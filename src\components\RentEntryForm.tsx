import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card } from './ui/card';
import { Calculator, Save, User, Building } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  getAllTenants,
  getTenant,
  createRentEntry,
  type Tenant,
  type RentEntry
} from '@/services/api';

export const RentEntryForm: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  const [formData, setFormData] = useState<Partial<RentEntry>>({
    rentMonth: new Date().toISOString().slice(0, 7), // Current month YYYY-MM
    building: '',
    floor: 'GF',
    rentDetails: {
      cashRent: 0,
      bankRent: 0,
      roomRent: 0,
      gst: 18,
      gstAmount: 0,
      totalRent: 0,
      totalPayable: 0
    },
    paymentStatus: 'paid'
  });

  const buildings = ['Ashirward Empro Park 5', 'Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const floors = ['GF', '1st', '2nd', '3rd', '4th', '5th'];

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      setIsLoading(true);
      const response = await getAllTenants();
      setTenants(response.data || []);
    } catch (error: any) {
      console.error('Error fetching tenants:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch tenants",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRent = (data: typeof formData) => {
    if (!data.rentDetails) return data;

    // Convert values to numbers and handle empty strings
    const cashRent = Number(data.rentDetails.cashRent) || 0;
    const bankRent = Number(data.rentDetails.bankRent) || 0;
    const roomRent = Number(data.rentDetails.roomRent) || 0;
    const gst = Number(data.rentDetails.gst) || 18;

    // Calculate GST amount based on bank rent
    const gstAmount = Math.round(bankRent * (gst / 100));

    // Calculate total rent (cash rent + bank rent only)
    const totalRent = cashRent + bankRent;

    // Calculate total payable (total rent + GST)
    const totalPayable = totalRent + gstAmount;

    return {
      ...data,
      rentDetails: {
        ...data.rentDetails,
        cashRent,
        bankRent,
        roomRent,
        gst,
        gstAmount,
        totalRent,
        totalPayable
      }
    };
  };

  const handleBuildingFloorChange = async (building: string, floor: string) => {
    // Find tenant for this building and floor
    const tenant = tenants.find(t => t.building === building && t.floor === floor);

    if (tenant) {
      setFormData(prev => ({
        ...prev,
        tenant: tenant.id,
        building,
        floor,
        rentDetails: {
          ...tenant.rentDetails,
          gst: tenant.rentDetails.gst || 18 // Use tenant's GST if available, otherwise default to 18
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        tenant: undefined,
        building,
        floor,
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        }
      }));
    }
  };

  const handleRentChange = (field: keyof typeof formData.rentDetails, value: number) => {
    const newFormData = {
      ...formData,
      rentDetails: {
        ...formData.rentDetails!,
        [field]: value
      }
    };

    const calculatedRent = calculateRent(newFormData);
    setFormData(calculatedRent);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.tenant) {
      toast({
        title: "Error",
        description: "No tenant found for this building and floor",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      // Format the rentMonth to include day (first day of the month)
      const rentEntryData = {
        ...formData,
        rentMonth: `${formData.rentMonth}-01`,
      } as RentEntry;

      await createRentEntry(rentEntryData);

      toast({
        title: "Success",
        description: `Rent entry for ${formData.rentMonth} saved successfully!`,
      });

      // Reset form with initial values to maintain controlled inputs
      setFormData({
        rentMonth: new Date().toISOString().slice(0, 7),
        building: '',
        floor: 'GF',
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        },
        paymentStatus: 'paid'
      });
    } catch (error: any) {
      console.error('Error saving rent entry:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save rent entry",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getTenantDetails = () => {
    if (!formData.tenant) return null;
    const tenant = tenants.find(t => t.id === formData.tenant);
    return tenant;
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Monthly Rent Entry</h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Month Selection */}
              <div>
                <Label htmlFor="rentMonth">Rent Month *</Label>
                <Input
                  id="rentMonth"
                  type="month"
                  value={formData.rentMonth}
                  onChange={(e) => setFormData(prev => ({ ...prev, rentMonth: e.target.value }))}
                  required
                />
              </div>

              {/* Property Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="building">Building *</Label>
                  <select
                    id="building"
                    value={formData.building}
                    onChange={(e) => handleBuildingFloorChange(e.target.value, formData.floor!)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select Building</option>
                    {buildings.map(building => (
                      <option key={building} value={building}>{building}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="floor">Floor *</Label>
                  <select
                    id="floor"
                    value={formData.floor}
                    onChange={(e) => handleBuildingFloorChange(formData.building!, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    {floors.map(floor => (
                      <option key={floor} value={floor}>{floor}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Auto-populated Tenant Information */}
              {formData.tenant && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <User className="h-5 w-5 text-green-600" />
                    <h4 className="font-medium text-green-800">Tenant Details</h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    {getTenantDetails() && (
                      <>
                        <div>
                          <span className="text-gray-600">Name:</span>
                          <span className="ml-2 font-medium">{getTenantDetails()?.fullName}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Firm:</span>
                          <span className="ml-2 font-medium">{getTenantDetails()?.firmName}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Phone:</span>
                          <span className="ml-2 font-medium">{getTenantDetails()?.phone}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Email:</span>
                          <span className="ml-2 font-medium">{getTenantDetails()?.email || '-'}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}

              {/* Rent Amount */}
              {formData.building && (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Rent Details - {formData.rentMonth}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <Label htmlFor="cashRent">Cash Rent (₹) *</Label>
                      <Input
                        id="cashRent"
                        type="number"
                        value={formData.rentDetails?.cashRent || ''}
                        onChange={(e) => handleRentChange('cashRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium"
                      />
                    </div>
                    <div>
                      <Label htmlFor="bankRent">Bank Rent (₹) *</Label>
                      <Input
                        id="bankRent"
                        type="number"
                        value={formData.rentDetails?.bankRent || ''}
                        onChange={(e) => handleRentChange('bankRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium"
                      />
                    </div>
                    <div>
                      <Label htmlFor="gst">GST (%)</Label>
                      <Input
                        id="gst"
                        type="number"
                        value={formData.rentDetails?.gst || ''}
                        onChange={(e) => handleRentChange('gst', Number(e.target.value))}
                        placeholder="18"
                        className="text-lg font-medium"
                      />
                    </div>
                    <div>
                      <Label htmlFor="roomRent">Room Rent (₹)</Label>
                      <Input
                        id="roomRent"
                        type="number"
                        value={formData.rentDetails?.roomRent || ''}
                        onChange={(e) => handleRentChange('roomRent', Number(e.target.value))}
                        placeholder="0"
                        className="text-lg font-medium"
                      />
                    </div>
                  </div>
                </div>
              )}

              {formData.building && (
                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3"
                  disabled={isLoading}
                >
                  <Save className="h-5 w-5 mr-2" />
                  {isLoading ? 'Saving...' : `Save Rent Entry for ${formData.rentMonth}`}
                </Button>
              )}
            </form>
          </Card>
        </div>

        {/* Preview Card */}
        <div className="lg:col-span-1">
          <Card className="p-6 sticky top-8">
            <div className="flex items-center space-x-2 mb-4">
              <Calculator className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">Rent Summary</h3>
            </div>

            {formData.building ? (
              <div className="space-y-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2 text-blue-700 mb-2">
                    <Building className="h-4 w-4" />
                    <span className="font-medium">Property</span>
                  </div>
                  <p className="text-sm">{formData.building} - {formData.floor}</p>
                  <p className="text-sm text-gray-600">Month: {formData.rentMonth}</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Cash Rent:</span>
                    <span className="font-medium">₹{formData.rentDetails?.cashRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Bank Rent:</span>
                    <span className="font-medium">₹{formData.rentDetails?.bankRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">GST ({formData.rentDetails?.gst}%):</span>
                    <span className="font-medium">₹{formData.rentDetails?.gstAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Room Rent:</span>
                    <span className="font-medium">₹{formData.rentDetails?.roomRent.toLocaleString()}</span>
                  </div>
                  <hr className="my-3" />
                  <div className="flex justify-between text-lg">
                    <span className="font-semibold text-gray-900">Total Rent:</span>
                    <span className="font-bold text-blue-600">₹{formData.rentDetails?.totalRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Total Payable (Inc. GST):</span>
                    <span className="font-medium text-green-600">₹{formData.rentDetails?.totalPayable.toLocaleString()}</span>
                  </div>
                </div>

                {getTenantDetails() && (
                  <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Tenant</h4>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">Name: {getTenantDetails()?.fullName}</p>
                      {getTenantDetails()?.firmName && (
                        <p className="text-sm text-gray-600">Firm: {getTenantDetails()?.firmName}</p>
                      )}
                      <p className="text-sm text-gray-600">Phone: {getTenantDetails()?.phone}</p>
                      <p className="text-sm text-gray-600">Email: {getTenantDetails()?.email || '-'}</p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">
                Select building and floor to view rent details
              </p>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};
