import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent } from './ui/card';
import { Calculator, Save, User, Building, Building2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  getAllTenants,
  getTenant,
  createRentEntry,
  type Tenant,
  type RentEntry
} from '@/services/api';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

export const RentEntryForm: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [tenants, setTenants] = useState<Tenant[]>([]);

  const [formData, setFormData] = useState<Partial<RentEntry>>({
    rentMonth: new Date().toISOString().slice(0, 7), // Current month YYYY-MM
    building: '',
    floor: 'GF',
    rentDetails: {
      cashRent: 0,
      bankRent: 0,
      roomRent: 0,
      gst: 18, // Fixed GST at 18%
      gstAmount: 0,
      totalRent: 0,
      totalPayable: 0
    },
    paymentStatus: 'paid'
  });

  const buildings = ['Plot 5 Ashirward Empro', 'Plot 6 Ashirward Empro'];
  const floors = ['GF', '1st', '2nd', '3rd', '4th', '5th'];

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      setIsLoading(true);
      const response = await getAllTenants();
      setTenants(response.data || []);
    } catch (error: any) {
      console.error('Error fetching tenants:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch tenants",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateRent = (data: typeof formData) => {
    if (!data.rentDetails) return data;

    // Convert values to numbers and handle empty strings
    const cashRent = Number(data.rentDetails.cashRent) || 0;
    const bankRent = Number(data.rentDetails.bankRent) || 0;
    const roomRent = Number(data.rentDetails.roomRent) || 0;
    const gst = 18; // Fixed GST at 18%

    // Calculate GST amount based on bank rent
    const gstAmount = Math.round(bankRent * (gst / 100));

    // Calculate total rent (cash rent + bank rent only)
    const totalRent = cashRent + bankRent;

    // Calculate total payable (total rent + GST)
    const totalPayable = totalRent + gstAmount;

    return {
      ...data,
      rentDetails: {
        ...data.rentDetails,
        cashRent,
        bankRent,
        roomRent,
        gst,
        gstAmount,
        totalRent,
        totalPayable
      }
    };
  };

  const handleBuildingFloorChange = async (building: string, floor: string) => {
    // Find tenant for this building and floor
    const tenant = tenants.find(t => t.building === building && t.floor === floor);

    if (tenant) {
      setFormData(prev => ({
        ...prev,
        tenant: tenant.id,
        building,
        floor,
        rentDetails: {
          ...tenant.rentDetails,
          gst: tenant.rentDetails.gst || 18 // Use tenant's GST if available, otherwise default to 18
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        tenant: undefined,
        building,
        floor,
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        }
      }));
    }
  };

  const handleRentChange = (field: keyof typeof formData.rentDetails, value: number) => {
    const newFormData = {
      ...formData,
      rentDetails: {
        ...formData.rentDetails!,
        [field]: value
      }
    };

    const calculatedRent = calculateRent(newFormData);
    setFormData(calculatedRent);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.tenant) {
      toast({
        title: "Error",
        description: "No tenant found for this building and floor",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      // Format the rentMonth to include day (first day of the month)
      const rentEntryData = {
        ...formData,
        rentMonth: `${formData.rentMonth}-01`,
      } as RentEntry;

      const response = await createRentEntry(rentEntryData);

      if (!response.success) {
        toast({
          title: "Error",
          description: response.message || "Failed to save rent entry",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: `Rent entry for ${formData.rentMonth} saved successfully!`,
      });

      // Reset form with initial values to maintain controlled inputs
      setFormData({
        rentMonth: new Date().toISOString().slice(0, 7),
        building: '',
        floor: 'GF',
        rentDetails: {
          cashRent: 0,
          bankRent: 0,
          roomRent: 0,
          gst: 18,
          gstAmount: 0,
          totalRent: 0,
          totalPayable: 0
        },
        paymentStatus: 'paid'
      });
    } catch (error: any) {
      console.error('Error saving rent entry:', error);
      toast({
        title: "Error",
        description: error.response?.data?.message || error.message || "Failed to save rent entry",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getTenantDetails = () => {
    if (!formData.tenant) return null;
    const tenant = tenants.find(t => t.id === formData.tenant);
    return tenant;
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Monthly Rent Entry</h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Month Selection */}
              <div>
                <Label htmlFor="rentMonth">Rent Month *</Label>
                <Input
                  id="rentMonth"
                  type="month"
                  value={formData.rentMonth}
                  onChange={(e) => setFormData(prev => ({ ...prev, rentMonth: e.target.value }))}
                  required
                  className="h-10 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 [color-scheme:light] dark:[color-scheme:dark]"
                />
              </div>

              {/* Property Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="building">Building *</Label>
                  <Select value={formData.building} onValueChange={(value) => handleBuildingFloorChange(value, formData.floor!)}>
                    <SelectTrigger id="building" className="w-full">
                      <SelectValue placeholder="Select Building" />
                    </SelectTrigger>
                    <SelectContent>
                      {buildings.map(building => (
                        <SelectItem key={building} value={building}>{building}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="floor">Floor *</Label>
                  <Select value={formData.floor} onValueChange={(value) => handleBuildingFloorChange(formData.building!, value)}>
                    <SelectTrigger id="floor" className="w-full">
                      <SelectValue placeholder="Select Floor" />
                    </SelectTrigger>
                    <SelectContent>
                      {floors.map(floor => (
                        <SelectItem key={floor} value={floor}>{floor}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Auto-populated Tenant Information */}
              {formData.tenant && (
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg space-y-3">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Tenant Details</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Name:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.fullName || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Firm:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.firmName || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Phone:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.phone || '-'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Email:</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.email || '-'}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Rent Amount */}
              {formData.building && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Calculator className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Rent Details - {formData.rentMonth}</h4>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <Label htmlFor="cashRent" className="text-gray-700 dark:text-gray-300">Cash Rent (₹) *</Label>
                      <Input
                        id="cashRent"
                        type="number"
                        value={formData.rentDetails?.cashRent || ''}
                        onChange={(e) => handleRentChange('cashRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                    <div>
                      <Label htmlFor="bankRent" className="text-gray-700 dark:text-gray-300">Bank Rent (₹) *</Label>
                      <Input
                        id="bankRent"
                        type="number"
                        value={formData.rentDetails?.bankRent || ''}
                        onChange={(e) => handleRentChange('bankRent', Number(e.target.value))}
                        placeholder="0"
                        required
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                    <div>
                      <Label htmlFor="gst" className="text-gray-700 dark:text-gray-300">GST (%)</Label>
                      <Input
                        id="gst"
                        type="number"
                        value={18}
                        disabled
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 cursor-not-allowed bg-gray-100"
                      />
                    </div>
                    <div>
                      <Label htmlFor="roomRent" className="text-gray-700 dark:text-gray-300">Room Rent (₹)</Label>
                      <Input
                        id="roomRent"
                        type="number"
                        value={formData.rentDetails?.roomRent || ''}
                        onChange={(e) => handleRentChange('roomRent', Number(e.target.value))}
                        placeholder="0"
                        className="text-lg font-medium dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                      />
                    </div>
                  </div>
                </div>
              )}

              {formData.building && (
                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3"
                  disabled={isLoading}
                >
                  <Save className="h-5 w-5 mr-2" />
                  {isLoading ? 'Saving...' : `Save Rent Entry for ${formData.rentMonth}`}
                </Button>
              )}
            </form>
          </Card>
        </div>

        {/* Preview Card */}
        <div className="lg:col-span-1">
          <Card className="sticky top-8 bg-white dark:bg-gray-800 border-0">
            <CardContent className="p-6 space-y-6">
              <div className="flex items-center gap-2">
                <Calculator className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <h3 className="font-medium text-gray-900 dark:text-gray-100">Rent Summary</h3>
              </div>

              {formData.building ? (
                <div className="space-y-6">
                  {/* Property Details */}
                  <div className="bg-blue-50/50 dark:bg-blue-900/10 rounded-lg p-4 space-y-1">
                    <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400 mb-2">
                      <Building2 className="h-4 w-4" />
                      <span className="font-medium">Property</span>
                    </div>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{formData.building} - {formData.floor}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Month: {formData.rentMonth}</p>
                  </div>

                  {/* Rent Breakdown */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Cash Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.cashRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Bank Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.bankRent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">GST ({formData.rentDetails?.gst}%):</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.gstAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Room Rent:</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">₹{formData.rentDetails?.roomRent.toLocaleString()}</span>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900 dark:text-gray-100">Total Rent:</span>
                        <span className="font-bold text-blue-600 dark:text-blue-400">₹{formData.rentDetails?.totalRent.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Total Payable (Inc. GST):</span>
                        <span className="font-medium text-green-600 dark:text-green-400">₹{formData.rentDetails?.totalPayable.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Tenant Details */}
                  {getTenantDetails() && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3 border border-gray-100 dark:border-gray-700">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">Tenant</h4>
                      <div className="space-y-2">
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Name:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.fullName}</p>
                        </div>
                        {getTenantDetails()?.firmName && (
                          <div className="space-y-1">
                            <p className="text-sm text-gray-500 dark:text-gray-400">Firm:</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.firmName}</p>
                          </div>
                        )}
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Phone:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.phone}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Email:</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{getTenantDetails()?.email || '-'}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building2 className="h-8 w-8 mx-auto mb-3 text-gray-400 dark:text-gray-600" />
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    Select building and floor to view rent details
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
